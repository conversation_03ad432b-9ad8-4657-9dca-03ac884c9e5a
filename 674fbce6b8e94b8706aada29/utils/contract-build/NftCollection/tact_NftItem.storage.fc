;;
;; Type: Context
;; TLB: _ bounced:bool sender:address value:int257 raw:^slice = Context
;;

_ $Context$_get_value((int, slice, int, slice) v) inline {
    var (v'bounced, v'sender, v'value, v'raw) = v;
    return v'value;
}

;;
;; Type: SendParameters
;; TLB: _ bounce:bool to:address value:int257 mode:int257 body:Maybe ^cell code:Maybe ^cell data:Maybe ^cell = SendParameters
;;

((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_value_mode_body(slice $to, int $value, int $mode, cell $body) inline {
    return (true, $to, $value, $mode, $body, null(), null());
}

((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_value_mode_bounce_body(slice $to, int $value, int $mode, int $bounce, cell $body) inline {
    return ($bounce, $to, $value, $mode, $body, null(), null());
}

((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_bounce_value_mode(slice $to, int $bounce, int $value, int $mode) inline {
    return ($bounce, $to, $value, $mode, null(), null(), null());
}

;;
;; Type: Transfer
;; Header: 0x5fcc3d14
;; TLB: transfer#5fcc3d14 query_id:uint64 new_owner:address response_destination:Maybe address custom_payload:Maybe ^cell forward_amount:coins forward_payload:remainder<slice> = Transfer
;;

(slice, ((int, slice, slice, cell, int, slice))) $Transfer$_load(slice sc_0) inline_ref {
    throw_unless(129, sc_0~load_uint(32) == 1607220500);
    var v'query_id = sc_0~load_uint(64);
    var v'new_owner = sc_0~__tact_load_address();
    var v'response_destination = sc_0~__tact_load_address_opt();
    var v'custom_payload = sc_0~load_int(1) ? sc_0~load_ref() : null();
    var v'forward_amount = sc_0~load_coins();
    var v'forward_payload = sc_0;
    return (sc_0, (v'query_id, v'new_owner, v'response_destination, v'custom_payload, v'forward_amount, v'forward_payload));
}

;;
;; Type: Excesses
;; Header: 0xd53276db
;; TLB: excesses#d53276db query_id:uint64 = Excesses
;;

builder $Excesses$_store(builder build_0, (int) v) inline {
    var (v'query_id) = v;
    build_0 = store_uint(build_0, 3576854235, 32);
    build_0 = build_0.store_uint(v'query_id, 64);
    return build_0;
}

cell $Excesses$_store_cell((int) v) inline {
    return $Excesses$_store(begin_cell(), v).end_cell();
}

((int)) $Excesses$_constructor_query_id(int $query_id) inline {
    return ($query_id);
}

;;
;; Type: GetStaticData
;; Header: 0x2fcb26a2
;; TLB: get_static_data#2fcb26a2 query_id:uint64 = GetStaticData
;;

(slice, ((int))) $GetStaticData$_load(slice sc_0) inline {
    throw_unless(129, sc_0~load_uint(32) == 801842850);
    var v'query_id = sc_0~load_uint(64);
    return (sc_0, (v'query_id));
}

;;
;; Type: ReportStaticData
;; Header: 0x8b771735
;; TLB: report_static_data#8b771735 query_id:uint64 index_id:int257 collection:address = ReportStaticData
;;

builder $ReportStaticData$_store(builder build_0, (int, int, slice) v) inline {
    var (v'query_id, v'index_id, v'collection) = v;
    build_0 = store_uint(build_0, 2339837749, 32);
    build_0 = build_0.store_uint(v'query_id, 64);
    build_0 = build_0.store_int(v'index_id, 257);
    build_0 = __tact_store_address(build_0, v'collection);
    return build_0;
}

cell $ReportStaticData$_store_cell((int, int, slice) v) inline {
    return $ReportStaticData$_store(begin_cell(), v).end_cell();
}

((int, int, slice)) $ReportStaticData$_constructor_query_id_index_id_collection(int $query_id, int $index_id, slice $collection) inline {
    return ($query_id, $index_id, $collection);
}

;;
;; Type: GetNftData
;; TLB: _ is_initialized:bool index:int257 collection_address:address owner_address:address individual_content:^cell = GetNftData
;;

(int, int, slice, slice, cell) $GetNftData$_to_external(((int, int, slice, slice, cell)) v) inline {
    var (v'is_initialized, v'index, v'collection_address, v'owner_address, v'individual_content) = v; 
    return (v'is_initialized, v'index, v'collection_address, v'owner_address, v'individual_content);
}

((int, int, slice, slice, cell)) $GetNftData$_constructor_is_initialized_index_collection_address_owner_address_individual_content(int $is_initialized, int $index, slice $collection_address, slice $owner_address, cell $individual_content) inline {
    return ($is_initialized, $index, $collection_address, $owner_address, $individual_content);
}

;;
;; Type: ProveOwnership
;; Header: 0x04ded148
;; TLB: prove_ownership#04ded148 query_id:uint64 dest:address forward_payload:^cell with_content:bool = ProveOwnership
;;

(slice, ((int, slice, cell, int))) $ProveOwnership$_load(slice sc_0) inline {
    throw_unless(129, sc_0~load_uint(32) == 81711432);
    var v'query_id = sc_0~load_uint(64);
    var v'dest = sc_0~__tact_load_address();
    var v'forward_payload = sc_0~load_ref();
    var v'with_content = sc_0~load_int(1);
    return (sc_0, (v'query_id, v'dest, v'forward_payload, v'with_content));
}

;;
;; Type: RequestOwner
;; Header: 0xd0c3bfea
;; TLB: request_owner#d0c3bfea query_id:uint64 dest:address forward_payload:^cell with_content:bool = RequestOwner
;;

(slice, ((int, slice, cell, int))) $RequestOwner$_load(slice sc_0) inline {
    throw_unless(129, sc_0~load_uint(32) == 3502489578);
    var v'query_id = sc_0~load_uint(64);
    var v'dest = sc_0~__tact_load_address();
    var v'forward_payload = sc_0~load_ref();
    var v'with_content = sc_0~load_int(1);
    return (sc_0, (v'query_id, v'dest, v'forward_payload, v'with_content));
}

;;
;; Type: OwnershipProof
;; Header: 0x0524c7ae
;; TLB: ownership_proof#0524c7ae query_id:uint64 item_id:uint256 owner:address data:^cell revoked_at:uint64 content:Maybe ^cell = OwnershipProof
;;

builder $OwnershipProof$_store(builder build_0, (int, int, slice, cell, int, cell) v) inline_ref {
    var (v'query_id, v'item_id, v'owner, v'data, v'revoked_at, v'content) = v;
    build_0 = store_uint(build_0, 86296494, 32);
    build_0 = build_0.store_uint(v'query_id, 64);
    build_0 = build_0.store_uint(v'item_id, 256);
    build_0 = __tact_store_address(build_0, v'owner);
    build_0 = build_0.store_ref(v'data);
    build_0 = build_0.store_uint(v'revoked_at, 64);
    build_0 = ~ null?(v'content) ? build_0.store_int(true, 1).store_ref(v'content) : build_0.store_int(false, 1);
    return build_0;
}

cell $OwnershipProof$_store_cell((int, int, slice, cell, int, cell) v) inline {
    return $OwnershipProof$_store(begin_cell(), v).end_cell();
}

((int, int, slice, cell, int, cell)) $OwnershipProof$_constructor_query_id_item_id_owner_data_revoked_at_content(int $query_id, int $item_id, slice $owner, cell $data, int $revoked_at, cell $content) inline {
    return ($query_id, $item_id, $owner, $data, $revoked_at, $content);
}

;;
;; Type: OwnerInfo
;; Header: 0x0dd607e3
;; TLB: owner_info#0dd607e3 query_id:uint64 item_id:uint256 initiator:address owner:address data:^cell revoked_at:uint64 content:Maybe ^cell = OwnerInfo
;;

builder $OwnerInfo$_store(builder build_0, (int, int, slice, slice, cell, int, cell) v) inline_ref {
    var (v'query_id, v'item_id, v'initiator, v'owner, v'data, v'revoked_at, v'content) = v;
    build_0 = store_uint(build_0, 232130531, 32);
    build_0 = build_0.store_uint(v'query_id, 64);
    build_0 = build_0.store_uint(v'item_id, 256);
    build_0 = __tact_store_address(build_0, v'initiator);
    build_0 = __tact_store_address(build_0, v'owner);
    build_0 = build_0.store_ref(v'data);
    build_0 = build_0.store_uint(v'revoked_at, 64);
    build_0 = ~ null?(v'content) ? build_0.store_int(true, 1).store_ref(v'content) : build_0.store_int(false, 1);
    return build_0;
}

cell $OwnerInfo$_store_cell((int, int, slice, slice, cell, int, cell) v) inline {
    return $OwnerInfo$_store(begin_cell(), v).end_cell();
}

((int, int, slice, slice, cell, int, cell)) $OwnerInfo$_constructor_query_id_item_id_initiator_owner_data_revoked_at_content(int $query_id, int $item_id, slice $initiator, slice $owner, cell $data, int $revoked_at, cell $content) inline {
    return ($query_id, $item_id, $initiator, $owner, $data, $revoked_at, $content);
}

;;
;; Type: Revoke
;; Header: 0x6f89f5e3
;; TLB: revoke#6f89f5e3 query_id:uint64 = Revoke
;;

(slice, ((int))) $Revoke$_load(slice sc_0) inline {
    throw_unless(129, sc_0~load_uint(32) == 1871312355);
    var v'query_id = sc_0~load_uint(64);
    return (sc_0, (v'query_id));
}

;;
;; Type: NftItem
;;

builder $NftItem$_store(builder build_0, (slice, int, int, slice, cell, slice, int) v) inline {
    var (v'collection_address, v'item_index, v'is_initialized, v'owner, v'individual_content, v'authority_address, v'revoked_at) = v;
    build_0 = __tact_store_address(build_0, v'collection_address);
    build_0 = build_0.store_int(v'item_index, 257);
    build_0 = build_0.store_int(v'is_initialized, 1);
    build_0 = __tact_store_address_opt(build_0, v'owner);
    build_0 = ~ null?(v'individual_content) ? build_0.store_int(true, 1).store_ref(v'individual_content) : build_0.store_int(false, 1);
    var build_1 = begin_cell();
    build_1 = __tact_store_address_opt(build_1, v'authority_address);
    build_1 = build_1.store_uint(v'revoked_at, 64);
    build_0 = store_ref(build_0, build_1.end_cell());
    return build_0;
}

(slice, ((slice, int, int, slice, cell, slice, int))) $NftItem$_load(slice sc_0) inline {
    var v'collection_address = sc_0~__tact_load_address();
    var v'item_index = sc_0~load_int(257);
    var v'is_initialized = sc_0~load_int(1);
    var v'owner = sc_0~__tact_load_address_opt();
    var v'individual_content = sc_0~load_int(1) ? sc_0~load_ref() : null();
    slice sc_1 = sc_0~load_ref().begin_parse();
    var v'authority_address = sc_1~__tact_load_address_opt();
    var v'revoked_at = sc_1~load_uint(64);
    return (sc_0, (v'collection_address, v'item_index, v'is_initialized, v'owner, v'individual_content, v'authority_address, v'revoked_at));
}

(slice, ((slice, int, slice))) $NftItem$init$_load(slice sc_0) inline {
    var v'collection_address = sc_0~__tact_load_address();
    var v'item_index = sc_0~load_int(257);
    var v'authority_address = sc_0~__tact_load_address();
    return (sc_0, (v'collection_address, v'item_index, v'authority_address));
}

(slice, int, int, slice, cell, slice, int) $NftItem$_contract_load() impure inline_ref {
    slice $sc = get_data().begin_parse();
    __tact_context_sys = $sc~load_ref();
    int $loaded = $sc~load_int(1);
    if ($loaded) {
        return $sc~$NftItem$_load();
    } else {
        ;; Allow only workchain deployments
        throw_unless(137, my_address().preload_uint(11) == 1024);
        (slice $collection_address, int $item_index, slice $authority_address) = $sc~$NftItem$init$_load();
        $sc.end_parse();
        return $NftItem$_contract_init($collection_address, $item_index, $authority_address);
    }
}

() $NftItem$_contract_store((slice, int, int, slice, cell, slice, int) v) impure inline {
    builder b = begin_cell();
    b = b.store_ref(__tact_context_sys);
    b = b.store_int(true, 1);
    b = $NftItem$_store(b, v);
    set_data(b.end_cell());
}