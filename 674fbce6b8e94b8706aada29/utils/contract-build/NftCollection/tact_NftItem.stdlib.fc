global (int, slice, int, slice) __tact_context;
global slice __tact_context_sender;
global cell __tact_context_sys;
global int __tact_randomized;

slice __tact_verify_address(slice address) impure inline {
    throw_unless(136, address.slice_bits() == 267);
    var h = address.preload_uint(11);
    throw_if(137, h == 1279);
    throw_unless(136, h == 1024);
    return address;
}

(slice, slice) __tact_load_address(slice cs) inline {
    slice raw = cs~load_msg_addr();
    return (cs, __tact_verify_address(raw));
}

(slice, slice) __tact_load_address_opt(slice cs) inline {
    if (cs.preload_uint(2) != 0) {
        slice raw = cs~load_msg_addr();
        return (cs, __tact_verify_address(raw));
    } else {
        cs~skip_bits(2);
        return (cs, null());
    }
}

builder __tact_store_address(builder b, slice address) inline {
    return b.store_slice(__tact_verify_address(address));
}

builder __tact_store_address_opt(builder b, slice address) inline {
    if (null?(address)) {
        b = b.store_uint(0, 2);
        return b;
    } else {
        return __tact_store_address(b, address);
    }
}

forall X -> X __tact_not_null(X x) impure inline {
    throw_if(128, null?(x)); return x;
}

(int, slice, int, slice) __tact_context_get() inline {
    return __tact_context;
}

slice __tact_context_get_sender() inline {
    return __tact_context_sender;
}

builder __tact_store_bool(builder b, int v) inline {
    return b.store_int(v, 1);
}

int __tact_slice_eq_bits(slice a, slice b) inline {
    return equal_slices_bits(a, b);
}

int __tact_slice_eq_bits_nullable_one(slice a, slice b) inline {
    return (null?(a)) ? (false) : (equal_slices_bits(a, b));
}

tuple __tact_string_builder_start(builder b) inline {
    return tpush(tpush(empty_tuple(), b), null());
}

tuple __tact_string_builder_start_string() inline {
    return __tact_string_builder_start(begin_cell());
}

cell __tact_string_builder_end(tuple builders) inline {
    (builder b, tuple tail) = uncons(builders);
    cell c = b.end_cell();
    while(~ null?(tail)) {
        (b, tail) = uncons(tail);
        c = b.store_ref(c).end_cell();
    }
    return c;
}

((tuple), ()) __tact_string_builder_append(tuple builders, slice sc) inline_ref {
    int sliceRefs = slice_refs(sc);
    int sliceBits = slice_bits(sc);
    
    while((sliceBits > 0) | (sliceRefs > 0)) {
    
        ;; Load the current builder
        (builder b, tuple tail) = uncons(builders);
        int remBytes = 127 - (builder_bits(b) / 8);
        int exBytes = sliceBits / 8;
    
        ;; Append bits
        int amount = min(remBytes, exBytes);
        if (amount > 0) {
            slice read = sc~load_bits(amount * 8);
            b = b.store_slice(read);
        }
    
        ;; Update builders
        builders = cons(b, tail);
    
        ;; Check if we need to add a new cell and continue
        if (exBytes - amount > 0) {
            var bb = begin_cell();
            builders = cons(bb, builders);
            sliceBits = (exBytes - amount) * 8;
        } elseif (sliceRefs > 0) {
            sc = sc~load_ref().begin_parse();
            sliceRefs = slice_refs(sc);
            sliceBits = slice_bits(sc);
        } else {
            sliceBits = 0;
            sliceRefs = 0;
        }
    }
    
    return ((builders), ());
}

builder $global_beginCell() impure asm """
    NEWC
""";

int $global_min(int $x, int $y) impure asm """
    MIN
""";

int $global_now() impure asm """
    NOW
""";

int $global_myBalance() impure asm """
    BALANCE FIRST
""";

int $global_nativeSendMessageReturnForwardFee(cell $msg, int $mode) impure asm """
    SENDMSG
""";

builder $Builder$_fun_storeCoins(builder $self, int $value) impure asm """
    STVARUINT16
""";

builder $Builder$_fun_storeRef(builder $self, cell $cell) impure asm($cell $self) """
    STREF
""";

cell $Builder$_fun_endCell(builder $self) impure asm """
    ENDC
""";

int $global_send((int, slice, int, int, cell, cell, cell) $params) impure inline_ref {
    var (($params'bounce, $params'to, $params'value, $params'mode, $params'body, $params'code, $params'data)) = $params;
    builder $b = $global_beginCell();
    $b = store_int($b, 1, 2);
    $b = __tact_store_bool($b, $params'bounce);
    $b = store_int($b, 0, 3);
    $b = __tact_store_address($b, $params'to);
    $b = $Builder$_fun_storeCoins($b, $params'value);
    $b = store_int($b, 0, 105);
    if (( ((~ null?($params'code))) ? (true) : ((~ null?($params'data))) )) {
        $b = __tact_store_bool($b, true);
        builder $bc = $global_beginCell();
        $bc = __tact_store_bool($bc, false);
        $bc = __tact_store_bool($bc, false);
        if ((~ null?($params'code))) {
            $bc = __tact_store_bool($bc, true);
            $bc = $Builder$_fun_storeRef($bc, __tact_not_null($params'code));
        } else {
            $bc = __tact_store_bool($bc, false);
        }
        if ((~ null?($params'data))) {
            $bc = __tact_store_bool($bc, true);
            $bc = $Builder$_fun_storeRef($bc, __tact_not_null($params'data));
        } else {
            $bc = __tact_store_bool($bc, false);
        }
        $bc = __tact_store_bool($bc, false);
        $b = __tact_store_bool($b, true);
        $b = $Builder$_fun_storeRef($b, $Builder$_fun_endCell($bc));
    } else {
        $b = __tact_store_bool($b, false);
    }
    cell $body = $params'body;
    if ((~ null?($body))) {
        $b = __tact_store_bool($b, true);
        $b = $Builder$_fun_storeRef($b, __tact_not_null($body));
    } else {
        $b = __tact_store_bool($b, false);
    }
    cell $c = $Builder$_fun_endCell($b);
    return $global_nativeSendMessageReturnForwardFee($c, $params'mode);
}

slice $Cell$_fun_beginParse(cell $self) impure asm """
    CTOS
""";

slice $Cell$_fun_asSlice(cell $self) impure inline {
    var ($self) = $self;
    return $Cell$_fun_beginParse($self);
}