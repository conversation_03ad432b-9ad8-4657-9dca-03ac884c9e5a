rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if a request is from an admin
    function isAdmin() {
      let userDoc = /databases/$(database)/documents/users/$(request.auth.uid);
      return request.auth != null && exists(userDoc) && get(userDoc).data.role == 'admin';
    }

    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user owns the resource
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    // Rules for the 'users' collection
    match /users/{userId} {
      allow read: if true; // Everyone can view user profiles
      allow create: if isAuthenticated() && request.auth.uid == userId; // Users can create their own profile
      allow update: if isOwner(userId) || isAdmin(); // Users can update their own profile, admins can update any
      allow delete: if isAdmin(); // Only admins can delete users
    }

    // Rules for the 'products' collection
    match /products/{productId} {
      allow read: if true; // Everyone can view products
      allow create: if isAuthenticated(); // Authenticated users can create products
      allow update: if isAuthenticated() && 
        (resource.data.sellerId == request.auth.uid || isAdmin()); // Sellers can update their products, admins can update any
      allow delete: if isAuthenticated() && 
        (resource.data.sellerId == request.auth.uid || isAdmin()); // Sellers can delete their products, admins can delete any
    }

    // Rules for the 'orders' collection
    match /orders/{orderId} {
      allow read: if isAuthenticated() && 
        (resource.data.buyerId == request.auth.uid || 
         resource.data.sellerId == request.auth.uid || 
         isAdmin()); // Buyers, sellers, and admins can view orders
      allow create: if isAuthenticated(); // Authenticated users can create orders
      allow update: if isAuthenticated() && 
        (resource.data.buyerId == request.auth.uid || 
         resource.data.sellerId == request.auth.uid || 
         isAdmin()); // Buyers, sellers, and admins can update orders
      allow delete: if isAdmin(); // Only admins can delete orders
    }

    // Rules for the 'categories' collection (if needed)
    match /categories/{categoryId} {
      allow read: if true; // Everyone can view categories
      allow create, update, delete: if isAdmin(); // Only admins can manage categories
    }
  }
}
