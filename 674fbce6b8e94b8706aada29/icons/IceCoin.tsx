import { IconProps } from "../utils/types";

const IceCoin: React.FC<IconProps> = ({ size = 24, className = "" }) => {
    const svgSize = `${size}px`;

    const s0 = '#f6d738';
    const s1 = '#f3cc30';
    const s2 = '#f7db5e';
    const s3 = '#0488cf';
    const s4 = '#ffffff';
    const s5 = '#abddf8';
    const s6 = '#fffeff';
    const s7 = '#e1f3fd';
    const s8 = '#9cd7f9';
    const s9 = '#fdfeff';

    return (
        <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 660 660" className={className} height={svgSize} width={svgSize}>
            <g id="Object">
                <g id="&lt;Group&gt;">
                    <g id="&lt;Group&gt;">
                        <g id="&lt;Group&gt;">
                            <path id="&lt;Path&gt;" fill={s0} d="m12.5 330c0-175.4 142.1-317.5 317.5-317.5 175.4 0 317.5 142.1 317.5 317.5 0 175.4-142.1 317.5-317.5 317.5-175.4 0-317.5-142.1-317.5-317.5z" />
                        </g>
                        <g id="&lt;Group&gt;">
                            <path id="&lt;Path&gt;" fill={s1} d="m60.5 330c0-148.8 120.7-269.5 269.5-269.5 148.8 0 269.5 120.7 269.5 269.5 0 148.8-120.7 269.5-269.5 269.5-148.8 0-269.5-120.7-269.5-269.5z" />
                        </g>
                        <g id="&lt;Group&gt;">
                        </g>
                        <g id="&lt;Group&gt;">
                            <path id="&lt;Path&gt;" fill={s2} d="m107.7 354.7c0-131.4 106.5-237.9 237.9-237.9 76.8 0 145.1 36.4 188.6 92.9-41.1-71.3-118.1-119.4-206.3-119.4-131.4 0-237.9 106.5-237.9 237.9 0 54.6 18.4 104.9 49.4 145-20.2-34.9-31.7-75.4-31.7-118.5z" />
                        </g>
                    </g>
                </g>
            </g>
            <filter id="f0">
                <feFlood flood-color="#e59538" flood-opacity="1" />
                <feBlend mode="hard-light" in2="SourceGraphic" />
                <feComposite in2="SourceAlpha" operator="in" />
            </filter>
            <g id="&lt;Group&gt; copy 17" filter="url(#f0)">
                <g id="&lt;Group&gt;">
                    <g id="&lt;Group&gt;">
                        <g id="&lt;Group&gt;">
                            <path id="&lt;Path&gt;" fill={s3} d="m476.5 266.2c-0.1-5.1-3.6-11.3-8-13.9l-129.2-75.1c-4.4-2.5-11.6-2.5-16 0l-130.2 75.2c-4.4 2.6-8 8.8-8 13.8l0.4 149.6c0.1 5.1 3.6 11.3 8 13.9l129.2 75.1c4.4 2.5 11.6 2.5 16 0l130.2-75.2c4.4-2.6 8-8.8 8-13.8z" />
                        </g>
                        <g id="&lt;Group&gt;">
                            <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m330.7 512.4c-4 0-7.9-0.9-10.9-2.7l-129.2-75.1c-6.1-3.5-10.8-11.6-10.8-18.7l-0.4-149.6c-0.1-7.2 4.6-15.3 10.8-18.9l130.3-75.2c2.9-1.7 6.8-2.6 10.8-2.6 4 0 7.9 0.9 10.9 2.7l129.2 75.1c6.1 3.5 10.8 11.6 10.8 18.7l0.4 149.7c0.1 7.1-4.6 15.2-10.8 18.8l-130.3 75.2c-2.9 1.7-6.8 2.6-10.8 2.6zm0.6-331.3c-2 0-3.9 0.4-5.1 1.1l-130.3 75.2c-2.6 1.5-5 5.8-5 8.8l0.4 149.6c0 3.1 2.5 7.4 5.1 8.9l129.2 75.1c1.2 0.7 3.2 1.1 5.1 1.1 2 0 3.9-0.4 5.1-1.1l130.3-75.2c2.6-1.5 5-5.8 5-8.8l-0.4-149.6c0-3.1-2.5-7.4-5.1-8.9l-129.2-75.1c-1.2-0.7-3.2-1.1-5.1-1.1z" />
                        </g>
                    </g>
                    <g id="&lt;Group&gt;">
                        <g id="&lt;Group&gt;">
                            <g id="&lt;Group&gt;">
                                <g id="&lt;Group&gt;">
                                    <path id="&lt;Path&gt;" fill={s4} d="m330.3 364.4c-0.1-12.7-9-28.2-20-34.6l-105.3-61.2c-11-6.4-19.9-1.2-19.8 11.4l0.3 122c0 12.7 9 28.3 20 34.6l105.3 61.2c10.9 6.4 19.9 1.2 19.8-11.4z" />
                                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m319.7 501.1q0 0 0 0c-2.9 0-6-0.9-9.2-2.8l-105.3-61.2c-11.2-6.5-20.2-22.2-20.3-35.1l-0.3-122c0-5 1.3-9.1 3.9-11.7 2-2 4.6-3 7.6-3 2.9 0 6 0.9 9.2 2.8l105.3 61.2c11.1 6.5 20.2 22.2 20.3 35.1l0.3 122c0 5-1.4 9.1-4 11.7-1.9 2-4.6 3-7.5 3zm-123.6-234.7c-2.7 0-5 1-6.7 2.7-2.4 2.4-3.7 6.2-3.7 10.9l0.4 122c0 12.5 8.8 27.8 19.6 34.1l105.4 61.2c3 1.8 5.9 2.7 8.6 2.7q0 0 0 0c2.6 0 5-1 6.7-2.7 2.4-2.4 3.7-6.2 3.7-10.9l-0.4-122c0-12.5-8.9-27.8-19.7-34.1l-105.3-61.2c-3-1.8-5.9-2.7-8.6-2.7z" />
                                </g>
                                <g id="&lt;Group&gt;">
                                    <path id="&lt;Path&gt;" fill={s5} d="m330.3 364.4c-0.1-12.7-9-28.2-20-34.6l-105.3-61.2c-11-6.4-19.9-1.2-19.8 11.4l0.3 122c0 12.7 9 28.3 20 34.6l105.3 61.2c10.9 6.4 19.9 1.2 19.8-11.4z" />
                                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m319.7 506.3q0 0 0 0c-3.8 0-7.8-1.2-11.8-3.5l-105.3-61.2c-12.8-7.4-22.8-24.8-22.9-39.5l-0.3-122c0-6.5 1.9-11.8 5.5-15.4 2.9-3 6.8-4.6 11.2-4.6 3.8 0 7.8 1.2 11.8 3.5l105.3 61.2c12.8 7.4 22.8 24.8 22.8 39.6l0.4 122c0 6.4-1.9 11.7-5.5 15.4-3 2.9-6.9 4.5-11.2 4.5zm-123.6-234.7q-1.9 0-3.1 1.2c-1.4 1.4-2.1 3.9-2.1 7.2l0.3 122c0.1 10.7 7.9 24.3 17.1 29.6l105.4 61.3c2.2 1.2 4.2 1.9 6 1.9q1.9 0 3.1-1.2c1.3-1.3 2.1-3.9 2.1-7.2l-0.4-122c0-10.7-7.8-24.2-17.1-29.6l-105.3-61.2c-2.2-1.3-4.3-2-6-2z" />
                                </g>
                            </g>
                            <g id="&lt;Group&gt;">
                                <g id="&lt;Group&gt;">
                                    <path id="&lt;Path&gt;" fill={s4} d="m456.5 268.5c11-6.4 11-16.7 0-23.1l-105.3-61.2c-10.9-6.4-28.9-6.4-39.8-0.1l-106.4 61.4c-10.9 6.4-11 16.7 0 23.1l105.3 61.2c11 6.4 28.9 6.4 39.8 0.1z" />
                                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m330.3 335.2q0 0 0 0c-7.7 0-14.9-1.8-20.3-4.9l-105.3-61.2c-5.5-3.2-8.5-7.5-8.5-12.1 0-4.5 3-8.8 8.5-12l106.4-61.4c5.4-3.1 12.5-4.8 20.1-4.8 7.7 0 14.9 1.7 20.3 4.9l105.3 61.2c5.5 3.2 8.5 7.5 8.5 12 0 4.6-3 8.9-8.5 12.1l-106.4 61.4c-5.3 3.1-12.5 4.8-20.1 4.8zm0.9-155.2c-7.4 0-14.3 1.6-19.5 4.6l-106.4 61.4c-5.1 3-7.9 6.9-7.9 11 0 4.2 2.8 8.1 7.9 11.1l105.3 61.2c5.2 3 12.2 4.7 19.7 4.7q0 0 0 0c7.4 0 14.4-1.6 19.6-4.6l106.3-61.4c5.1-3 8-6.9 8-11.1 0-4.1-2.8-8-7.9-11l-105.4-61.2c-5.2-3.1-12.2-4.7-19.7-4.7z" />
                                </g>
                                <g id="&lt;Group&gt;">
                                    <path id="&lt;Path&gt;" fill={s5} d="m456.5 268.5c11-6.4 11-16.7 0-23.1l-105.3-61.2c-10.9-6.4-28.9-6.4-39.8-0.1l-106.4 61.4c-10.9 6.4-11 16.7 0 23.1l105.3 61.2c11 6.4 28.9 6.4 39.8 0.1z" />
                                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m330.3 340.3q0 0 0 0c-8.6 0-16.7-1.9-22.9-5.5l-105.3-61.2c-7-4.1-11.1-10.1-11.1-16.6 0-6.4 4.1-12.4 11.1-16.5l106.4-61.4c6.1-3.5 14.2-5.5 22.7-5.5 8.6 0 16.7 2 22.9 5.6l105.3 61.2c7.1 4.1 11.1 10.1 11.1 16.5 0 6.5-4.1 12.5-11.1 16.5l-106.4 61.4c-6.1 3.6-14.2 5.5-22.7 5.5zm0.9-155.2c-6.4 0-12.6 1.5-16.9 4l-106.4 61.4c-3.4 2-5.4 4.4-5.4 6.6 0 2.2 2 4.5 5.4 6.5l105.3 61.2c4.4 2.6 10.6 4 17.1 4q0 0 0 0c6.4 0 12.6-1.4 17-3.9l106.3-61.4c3.4-2 5.4-4.4 5.4-6.6 0-2.2-2-4.6-5.3-6.5l-105.4-61.3c-4.4-2.5-10.6-4-17.1-4z" />
                                </g>
                            </g>
                            <g id="&lt;Group&gt;">
                                <g id="&lt;Group&gt;">
                                    <path id="&lt;Path&gt;" fill={s4} d="m350.1 329.9c-10.9 6.3-19.9 21.8-19.8 34.5l0.3 122c0.1 12.6 9.1 17.8 20 11.5l106.4-61.4c10.9-6.4 19.9-21.9 19.8-34.5l-0.3-122c0-12.7-9-17.9-20-11.5z" />
                                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m341.8 501.2q0 0 0 0c-7.1 0-11.7-5.8-11.7-14.8l-0.4-122c0-12.9 9-28.6 20.2-35l106.3-61.4c3.2-1.9 6.3-2.8 9.2-2.8 7.1 0 11.7 5.8 11.7 14.8l0.3 122c0.1 12.8-9 28.5-20.1 35l-106.4 61.4c-3.2 1.8-6.3 2.8-9.1 2.8zm123.6-234.9c-2.7 0-5.6 0.9-8.6 2.7l-106.4 61.4c-10.8 6.2-19.6 21.5-19.5 34l0.3 122c0 8.4 4.1 13.6 10.6 13.6q0 0 0 0c2.6 0 5.5-0.9 8.5-2.6l106.4-61.4c10.8-6.3 19.6-21.5 19.6-34l-0.4-122c0-8.4-4-13.7-10.5-13.7z" />
                                </g>
                                <g id="&lt;Group&gt;">
                                    <path id="&lt;Path&gt;" fill={s5} d="m350.1 329.9c-10.9 6.3-19.9 21.8-19.8 34.5l0.3 122c0.1 12.6 9.1 17.8 20 11.5l106.4-61.4c10.9-6.4 19.9-21.9 19.8-34.5l-0.3-122c0-12.7-9-17.9-20-11.5z" />
                                    <path id="&lt;Compound Path&gt;" fill-rule="evenodd" fill={s3} d="m341.8 506.3q0 0 0 0c-10.1 0-16.9-8-16.9-19.9l-0.4-122c0-14.8 10-32.1 22.8-39.5l106.3-61.4c4-2.3 8-3.5 11.8-3.5 10 0 16.8 8 16.9 19.9l0.3 122c0 14.8-9.9 32.2-22.7 39.6l-106.4 61.4c-4 2.3-7.9 3.4-11.7 3.4zm123.6-234.8c-1.8 0-3.8 0.7-6 1.9l-106.4 61.4c-9.2 5.4-17 18.9-17 29.6l0.4 122c0 2.5 0.5 8.4 5.4 8.4q0 0 0 0c1.7 0 3.8-0.6 5.9-1.9l106.4-61.4c9.2-5.3 17-18.9 17-29.5l-0.3-122c-0.1-2.6-0.6-8.5-5.4-8.5z" />
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
                <g id="&lt;Group&gt;">
                    <path id="&lt;Path&gt;" fill={s6} d="m202.4 408.5c-0.8-0.7-1.5-2.6-1.6-4.7-0.4-8.1 0-17.5-0.8-26.2-1.9-18.5-1.3-36.1 0.2-54.6 0.7-8.2-5-27.1 5.8-31.2 4.9-1.9 14.1 3.8 18.4 5.6 8 3.3 16.1 6.4 23.5 10.8 4.1 2.5 22.4 12.7 7.2 13.1-12.4 0.2-25.1-6.5-36.8 0.4-8.8 5.1-10.9 15.2-11.9 24.4-1.2 11.8-2.1 23.7-0.8 35.5 0.9 7.9 3.7 17.4 0 25-1.1 2.3-2.3 2.7-3.2 1.9z" />
                </g>
                <path id="&lt;Path&gt;" fill={s7} d="m324.7 210.6c-5-2.3-12-3.1-20.4-1.4-17.9 3.5-35.8 19.6-49.7 30.9-19.5 15.8-22.8 22.9-4.1 39.3 13 11.6 27.7 20 43.6 27.1 15.6 7.1 31.4 13.2 48.1 6.6 16-6.3 34.5-9.6 49.1-18.7 4.7-3 27.6-18.6 12.9-24.5-5.1-2-16.8 1.9-21.6 4.1-21.7 9.8-55.7 41.7-80.2 20.8-21.3-18.2 10.7-35.9 21-47 14.8-15.9 13.5-31.4 1.3-37.2z" />
                <path id="&lt;Path&gt;" fill={s7} d="m398.5 225.3c8.5 4.4 15.4 10.1 24.3 13.7 8.7 3.5 32.6 12.7 17.3 23.8-3.2 2.2-15.6 12.5-16.8 2.6-0.3-2.9 7.9-6.5 7.5-10.8-0.4-3.5-4.5-4.8-7.2-6.3-9.3-4.9-20-8.4-28.7-14.2-2.2-1.5-7.1-5.1-6.1-8.5 1-3.8 5.2-2.8 9.7-0.3z" />
                <path id="&lt;Path&gt;" fill={s8} d="m322.8 493.6c1.3-1.3 2.1-3.9 2.1-7.2l-0.4-122c0-6.8-3.1-14.7-7.8-20.9q-2.6 0.9-4.9 2.2l-106.3 61.4c-6.4 3.7-9 8.7-8.1 13.5 3 4.7 6.8 8.7 10.9 11l105.4 61.3q0.1 0 0.3 0.1 2.4 1 5.1 1.8c0.2 0 0.4 0 0.6 0q1.9 0 3.1-1.2z" />
                <path id="&lt;Path&gt;" fill={s8} d="m336.4 486.4c0 2.5 0.5 8.4 5.4 8.4q0 0 0 0 0.2 0 0.4 0 3-0.8 5.6-1.9l106.3-61.4c4.1-2.4 7.9-6.3 10.8-10.9 1.1-4.8-1.5-9.9-7.9-13.6l-105.4-61.2c-2.1-1.3-4.6-2.3-7.3-3.1-4.9 6.4-8.3 14.6-8.3 21.7z" />
                <path id="&lt;Path&gt;" fill={s9} d="m459.1 304.9c7 1.6 5.2 13.3 5 18.2-0.3 9.5 0.7 18.9 1.6 28.3 0.6 6.9 0.9 13.8 0.7 20.8-0.2 7.8-0.9 14.5-2.5 22-5.2 25.6-27.2 38.4-48.5 49.1-9 4.5-17.9 9.1-26.9 13.7-2.9 1.5-15.1 9.3-18.5 5.2-1.4-1.7 24.4-15.3 26.7-16.4 6.4-3.2 12.9-6.1 19.2-9.3 12.6-6.3 24.9-14 33.3-25.7 15.4-21.2 1.3-51 0.4-74.5-0.3-7.4 0-17.3 2.5-24.3 0.7-2.2 2.1-6.2 4.6-6.9q1.4-0.4 2.4-0.2z" />
            </g>
        </svg>
    );
};

export default IceCoin;