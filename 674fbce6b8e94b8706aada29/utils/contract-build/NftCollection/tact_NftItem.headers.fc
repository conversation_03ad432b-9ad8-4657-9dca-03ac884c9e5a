;;
;; Header files for NftItem
;; NOTE: declarations are sorted for optimal order
;;

;; __tact_verify_address
slice __tact_verify_address(slice address) impure inline;

;; __tact_load_address
(slice, slice) __tact_load_address(slice cs) inline;

;; __tact_load_address_opt
(slice, slice) __tact_load_address_opt(slice cs) inline;

;; __tact_store_address
builder __tact_store_address(builder b, slice address) inline;

;; __tact_store_address_opt
builder __tact_store_address_opt(builder b, slice address) inline;

;; __tact_not_null
forall X -> X __tact_not_null(X x) impure inline;

;; __tact_context_get
(int, slice, int, slice) __tact_context_get() inline;

;; __tact_context_get_sender
slice __tact_context_get_sender() inline;

;; __tact_store_bool
builder __tact_store_bool(builder b, int v) inline;

;; __tact_slice_eq_bits
int __tact_slice_eq_bits(slice a, slice b) inline;

;; __tact_slice_eq_bits_nullable_one
int __tact_slice_eq_bits_nullable_one(slice a, slice b) inline;

;; __tact_string_builder_start
tuple __tact_string_builder_start(builder b) inline;

;; __tact_string_builder_start_string
tuple __tact_string_builder_start_string() inline;

;; __tact_string_builder_end
cell __tact_string_builder_end(tuple builders) inline;

;; __tact_string_builder_append
((tuple), ()) __tact_string_builder_append(tuple builders, slice sc) inline_ref;

;; $Transfer$_load
(slice, ((int, slice, slice, cell, int, slice))) $Transfer$_load(slice sc_0) inline_ref;

;; $Excesses$_store
builder $Excesses$_store(builder build_0, (int) v) inline;

;; $Excesses$_store_cell
cell $Excesses$_store_cell((int) v) inline;

;; $GetStaticData$_load
(slice, ((int))) $GetStaticData$_load(slice sc_0) inline;

;; $ReportStaticData$_store
builder $ReportStaticData$_store(builder build_0, (int, int, slice) v) inline;

;; $ReportStaticData$_store_cell
cell $ReportStaticData$_store_cell((int, int, slice) v) inline;

;; $ProveOwnership$_load
(slice, ((int, slice, cell, int))) $ProveOwnership$_load(slice sc_0) inline;

;; $RequestOwner$_load
(slice, ((int, slice, cell, int))) $RequestOwner$_load(slice sc_0) inline;

;; $OwnershipProof$_store
builder $OwnershipProof$_store(builder build_0, (int, int, slice, cell, int, cell) v) inline_ref;

;; $OwnershipProof$_store_cell
cell $OwnershipProof$_store_cell((int, int, slice, cell, int, cell) v) inline;

;; $OwnerInfo$_store
builder $OwnerInfo$_store(builder build_0, (int, int, slice, slice, cell, int, cell) v) inline_ref;

;; $OwnerInfo$_store_cell
cell $OwnerInfo$_store_cell((int, int, slice, slice, cell, int, cell) v) inline;

;; $Revoke$_load
(slice, ((int))) $Revoke$_load(slice sc_0) inline;

;; $NftItem$_store
builder $NftItem$_store(builder build_0, (slice, int, int, slice, cell, slice, int) v) inline;

;; $NftItem$_load
(slice, ((slice, int, int, slice, cell, slice, int))) $NftItem$_load(slice sc_0) inline;

;; $Context$_get_value
_ $Context$_get_value((int, slice, int, slice) v) inline;

;; $GetNftData$_to_external
(int, int, slice, slice, cell) $GetNftData$_to_external(((int, int, slice, slice, cell)) v) inline;

;; $NftItem$init$_load
(slice, ((slice, int, slice))) $NftItem$init$_load(slice sc_0) inline;

;; $NftItem$_contract_init
(slice, int, int, slice, cell, slice, int) $NftItem$_contract_init(slice $collection_address, int $item_index, slice $authority_address) impure inline_ref;

;; $NftItem$_contract_load
(slice, int, int, slice, cell, slice, int) $NftItem$_contract_load() impure inline_ref;

;; $NftItem$_contract_store
() $NftItem$_contract_store((slice, int, int, slice, cell, slice, int) v) impure inline;

;; $global_send
int $global_send((int, slice, int, int, cell, cell, cell) $params) impure inline_ref;

;; $Cell$_fun_asSlice
slice $Cell$_fun_asSlice(cell $self) impure inline;

;; $NftItem$_fun_msgValue
((slice, int, int, slice, cell, slice, int), int) $NftItem$_fun_msgValue((slice, int, int, slice, cell, slice, int) $self, int $value) impure inline_ref;

;; $GetNftData$_constructor_is_initialized_index_collection_address_owner_address_individual_content
((int, int, slice, slice, cell)) $GetNftData$_constructor_is_initialized_index_collection_address_owner_address_individual_content(int $is_initialized, int $index, slice $collection_address, slice $owner_address, cell $individual_content) inline;

;; $NftItem$_fun_get_nft_data
((slice, int, int, slice, cell, slice, int), (int, int, slice, slice, cell)) $NftItem$_fun_get_nft_data((slice, int, int, slice, cell, slice, int) $self) impure inline_ref;

;; $NftItem$_fun_get_authority_address
((slice, int, int, slice, cell, slice, int), slice) $NftItem$_fun_get_authority_address((slice, int, int, slice, cell, slice, int) $self) impure inline_ref;

;; $NftItem$_fun_get_revoked_time
((slice, int, int, slice, cell, slice, int), int) $NftItem$_fun_get_revoked_time((slice, int, int, slice, cell, slice, int) $self) impure inline_ref;

;; $SendParameters$_constructor_to_value_mode_body
((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_value_mode_body(slice $to, int $value, int $mode, cell $body) inline;

;; $Excesses$_constructor_query_id
((int)) $Excesses$_constructor_query_id(int $query_id) inline;

;; $SendParameters$_constructor_to_value_mode_bounce_body
((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_value_mode_bounce_body(slice $to, int $value, int $mode, int $bounce, cell $body) inline;

;; $ReportStaticData$_constructor_query_id_index_id_collection
((int, int, slice)) $ReportStaticData$_constructor_query_id_index_id_collection(int $query_id, int $index_id, slice $collection) inline;

;; $OwnershipProof$_constructor_query_id_item_id_owner_data_revoked_at_content
((int, int, slice, cell, int, cell)) $OwnershipProof$_constructor_query_id_item_id_owner_data_revoked_at_content(int $query_id, int $item_id, slice $owner, cell $data, int $revoked_at, cell $content) inline;

;; $OwnerInfo$_constructor_query_id_item_id_initiator_owner_data_revoked_at_content
((int, int, slice, slice, cell, int, cell)) $OwnerInfo$_constructor_query_id_item_id_initiator_owner_data_revoked_at_content(int $query_id, int $item_id, slice $initiator, slice $owner, cell $data, int $revoked_at, cell $content) inline;

;; $SendParameters$_constructor_to_bounce_value_mode
((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_bounce_value_mode(slice $to, int $bounce, int $value, int $mode) inline;
