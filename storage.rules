rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {

    // Helper function to check if a request is from an admin
    function isAdmin() {
      let userDoc = firestore.get(/databases/(default)/documents/users/$(request.auth.uid));
      return request.auth != null && userDoc != null && userDoc.data.role == 'admin';
    }

    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
  
    // Allow authenticated users to read all files
    match /{allPaths=**} {
      allow read: if true; // Public read access for product images, etc.
      allow create, update, delete: if isAdmin();
    }

    // User profile images - users can manage their own
    match /users/{userId}/{allPaths=**} {
      allow read: if true;
      allow create, update, delete: if isAuthenticated() && 
        (request.auth.uid == userId || isAdmin());
    }

    // Product images - sellers can manage their own product images
    match /products/{productId}/{allPaths=**} {
      allow read: if true;
      allow create, update, delete: if isAuthenticated(); // Any authenticated user can upload product images
    }

    // General marketplace assets
    match /marketplace/{allPaths=**} {
      allow read: if true;
      allow create, update, delete: if isAdmin();
    }
  }
}
