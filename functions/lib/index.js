"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserProfile = exports.createUserRecord = void 0;
const admin = require("firebase-admin");
const functions = require("firebase-functions");
admin.initializeApp();
const db = admin.firestore();
exports.createUserRecord = functions.auth.user().onCreate(async (user) => {
    const userRecord = {
        id: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        role: "user",
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
    };
    try {
        await db.collection("users").doc(user.uid).set(userRecord);
        console.log(`User record created for ${user.uid}`);
    }
    catch (error) {
        console.error("Error creating user record:", error);
        throw new functions.https.HttpsError("internal", error.message ||
            "Server error while creating user record.");
    }
});
// Example function for getting user profile
exports.getUserProfile = functions.https.onCall(async (data, context) => {
    // Ensure the user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "Authentication required to get user profile.");
    }
    try {
        const userDoc = await db.collection("users").doc(context.auth.uid).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError("not-found", "User profile not found.");
        }
        return userDoc.data();
    }
    catch (error) {
        console.error("Error getting user profile:", error);
        throw new functions.https.HttpsError("internal", error.message || "Server error while getting user profile.");
    }
});
//# sourceMappingURL=index.js.map