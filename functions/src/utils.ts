import { Timestamp } from "firebase-admin/firestore";

/**
 * Converts a Firebase Timestamp to a JavaScript Date object
 */
export function firebaseTimestampToDate(timestamp: Timestamp): Date {
  return timestamp.toDate();
}

/**
 * Formats a Firebase Timestamp or Date to a Firebase Timestamp
 */
export function formatDateToFirebaseTimestamp(date: Date | Timestamp): Timestamp {
  if (date instanceof Timestamp) {
    return date;
  }
  return Timestamp.fromDate(date);
}

/**
 * Generates a unique ID for documents
 */
export function generateUniqueId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

/**
 * Validates email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
