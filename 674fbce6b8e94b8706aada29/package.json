{"name": "clicker-telegram-app-visual", "version": "0.1.0", "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "prisma generate && next build"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@orbs-network/ton-access": "^2.3.3", "@prisma/client": "^5.17.0", "@telegram-apps/sdk": "^1.1.3", "@ton/core": "^0.59.0", "@ton/ton": "^15.1.0", "@tonconnect/ui-react": "^2.0.8", "@twa-dev/sdk": "^7.0.0", "@types/crypto-js": "^4.2.2", "@types/ua-parser-js": "^0.7.39", "crypto-js": "^4.2.0", "mongodb": "^6.8.0", "next": "14.2.5", "prisma": "^5.17.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "telegram": "^2.23.2", "tsconfig-paths": "^4.2.0", "ua-parser-js": "^1.0.38", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}