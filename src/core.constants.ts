export enum CollectionStatus {
  PRELAUNCH = "PRELAUNCH",
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export interface Collection {
  id: string;
  collectionId: string;
  name: string;
  logoUrl: string;
  description: string;
  status: CollectionStatus;
  createdAt: Date;
  updatedAt: Date;
}

export const COLLECTION_STATUS_TEXT = {
  [CollectionStatus.PRELAUNCH]: "Pre-launch",
  [CollectionStatus.ACTIVE]: "Active",
  [CollectionStatus.DELETED]: "Deleted",
};

export enum Role {
  ADMIN = "admin",
  USER = "user",
}

export interface UserEntity {
  id: string;
  name?: string;
  role?: "admin" | "user";
  tg_id?: string;
  ton_wallet_address?: string;
}

export enum OrderStatus {
  CREATED = "created",
  ACTIVE = "active",
  PAID = "paid",
  FULFILLED = "fulfilled",
}

export interface OrderEntity {
  collection_id: string;
  item_id?: string;
  creator_id: string;
  price: number;
  buyer_id?: string;
  status: OrderStatus;
}

export interface TxLookup {
  last_checked_record_id: string;
}
