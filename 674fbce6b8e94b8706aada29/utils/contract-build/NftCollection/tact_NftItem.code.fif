PROGRAM{
  DECLPROC __tact_verify_address
  DECLPROC __tact_load_address
  DECLPROC __tact_load_address_opt
  DECLPROC __tact_store_address
  DECLPROC __tact_store_address_opt
  DECLPROC __tact_not_null
  DECLPROC __tact_context_get
  DECLPROC __tact_context_get_sender
  DECLPROC __tact_store_bool
  DECLPROC __tact_slice_eq_bits
  DECLPROC __tact_slice_eq_bits_nullable_one
  DECLPROC __tact_string_builder_start
  DECLPROC __tact_string_builder_start_string
  DECLPROC __tact_string_builder_end
  DECLPROC __tact_string_builder_append
  DECLPROC $Transfer$_load
  DECLPROC $Excesses$_store
  DECLPROC $Excesses$_store_cell
  DECLPROC $GetStaticData$_load
  DECLPROC $ReportStaticData$_store
  DECLPROC $ReportStaticData$_store_cell
  DECLPROC $ProveOwnership$_load
  DECLPROC $RequestOwner$_load
  DECLPROC $OwnershipProof$_store
  DECLPROC $OwnershipProof$_store_cell
  DECLPROC $OwnerInfo$_store
  DECLPROC $OwnerInfo$_store_cell
  DECLPROC $Revoke$_load
  DECLPROC $NftItem$_store
  DECLPROC $NftItem$_load
  DECLPROC $Context$_get_value
  DECLPROC $GetNftData$_to_external
  DECLPROC $NftItem$init$_load
  DECLPROC $NftItem$_contract_init
  DECLPROC $NftItem$_contract_load
  DECLPROC $NftItem$_contract_store
  DECLPROC $global_send
  DECLPROC $Cell$_fun_asSlice
  DECLPROC $NftItem$_fun_msgValue
  DECLPROC $GetNftData$_constructor_is_initialized_index_collection_address_owner_address_individual_content
  DECLPROC $NftItem$_fun_get_nft_data
  DECLPROC $NftItem$_fun_get_authority_address
  DECLPROC $NftItem$_fun_get_revoked_time
  DECLPROC $SendParameters$_constructor_to_value_mode_body
  DECLPROC $Excesses$_constructor_query_id
  DECLPROC $SendParameters$_constructor_to_value_mode_bounce_body
  DECLPROC $ReportStaticData$_constructor_query_id_index_id_collection
  DECLPROC $OwnershipProof$_constructor_query_id_item_id_owner_data_revoked_at_content
  DECLPROC $OwnerInfo$_constructor_query_id_item_id_initiator_owner_data_revoked_at_content
  DECLPROC $SendParameters$_constructor_to_bounce_value_mode
  DECLPROC $NftItem$_internal_binary_Transfer
  DECLPROC $NftItem$_internal_binary_GetStaticData
  DECLPROC $NftItem$_internal_binary_ProveOwnership
  DECLPROC $NftItem$_internal_binary_RequestOwner
  DECLPROC $NftItem$_internal_text_986c2ba124bb9287eb4a0bd8d3104e1c0067a3c93952d889c74d08185bd30d4d
  DECLPROC $NftItem$_internal_binary_Revoke
  102351 DECLMETHOD %get_nft_data
  84760 DECLMETHOD %get_authority_address
  97667 DECLMETHOD %get_revoked_time
  115390 DECLMETHOD lazy_deployment_completed
  DECLPROC $NftItem$_contract_router_internal
  DECLPROC recv_internal
  DECLGLOBVAR __tact_context
  DECLGLOBVAR __tact_context_sender
  DECLGLOBVAR __tact_context_sys
  DECLGLOBVAR __tact_randomized
  __tact_verify_address PROCINLINE:<{
    DUP
    SBITS
    267 PUSHINT
    EQUAL
    136 THROWIFNOT
    DUP
    11 PLDU
    DUP
    1279 PUSHINT
    EQUAL
    137 THROWIF
    10 PUSHPOW2
    EQUAL
    136 THROWIFNOT
  }>
  __tact_load_address PROCINLINE:<{
    LDMSGADDR
    SWAP
    __tact_verify_address INLINECALLDICT
  }>
  __tact_load_address_opt PROCINLINE:<{
    DUP
    2 PLDU
    0 NEQINT
    IF:<{
      LDMSGADDR
      SWAP
      __tact_verify_address INLINECALLDICT
    }>ELSE<{
      2 PUSHINT
      SDSKIPFIRST
      PUSHNULL
    }>
  }>
  __tact_store_address PROCINLINE:<{
    __tact_verify_address INLINECALLDICT
    STSLICER
  }>
  __tact_store_address_opt PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      0 PUSHINT
      SWAP
      2 STU
    }>ELSE<{
      __tact_store_address INLINECALLDICT
    }>
  }>
  __tact_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
  }>
  __tact_context_get PROCINLINE:<{
    __tact_context GETGLOB
    4 UNTUPLE
  }>
  __tact_context_get_sender PROCINLINE:<{
    __tact_context_sender GETGLOB
  }>
  __tact_store_bool PROCINLINE:<{
    SWAP
    1 STI
  }>
  __tact_slice_eq_bits PROCINLINE:<{
    SDEQ
  }>
  __tact_slice_eq_bits_nullable_one PROCINLINE:<{
    OVER
    ISNULL
    IF:<{
      2DROP
      FALSE
    }>ELSE<{
      SDEQ
    }>
  }>
  __tact_string_builder_start PROCINLINE:<{
    NIL
    SWAP
    TPUSH
    PUSHNULL
    TPUSH
  }>
  __tact_string_builder_start_string PROCINLINE:<{
    NEWC
    __tact_string_builder_start INLINECALLDICT
  }>
  __tact_string_builder_end PROCINLINE:<{
    UNCONS
    SWAP
    ENDC
    WHILE:<{
      OVER
      ISNULL
      NOT
    }>DO<{
      SWAP
      UNCONS
      -ROT
      STREF
      ENDC
    }>
    NIP
  }>
  __tact_string_builder_append PROCREF:<{
    DUP
    SREFS
    OVER
    SBITS
    WHILE:<{
      DUP
      0 GTINT
      s2 PUSH
      0 GTINT
      OR
    }>DO<{
      s0 s3 XCHG
      UNCONS
      127 PUSHINT
      s2 PUSH
      BBITS
      3 RSHIFT#
      SUB
      s0 s5 XCHG
      3 RSHIFT#
      s5 s5 XCPU
      MIN
      DUP
      0 GTINT
      IF:<{
        DUP
        3 LSHIFT#
        s1 s5 XCHG
        LDSLICEX
        s3 s3 XCHG2
        STSLICER
        s0 s1 s4 XCHG3
      }>
      -ROT
      CONS
      s4 s1 PUSH2
      SUB
      0 GTINT
      IF:<{
        NEWC
        SWAP
        CONS
        s4 s4 XCHG2
        SUB
        3 LSHIFT#
      }>ELSE<{
        NIP
        s3 POP
        0 GTINT
        IF:<{
          LDREF
          DROP
          CTOS
          DUP
          SREFS
          OVER
          SBITS
        }>ELSE<{
          0 PUSHINT
          DUP
        }>
      }>
    }>
    3 BLKDROP
  }>
  $Transfer$_load PROCREF:<{
    32 LDU
    SWAP
    1607220500 PUSHINT
    EQUAL
    129 THROWIFNOT
    64 LDU
    __tact_load_address INLINECALLDICT
    SWAP
    __tact_load_address_opt INLINECALLDICT
    SWAP
    1 LDI
    SWAP
    IF:<{
      LDREF
    }>ELSE<{
      PUSHNULL
      SWAP
    }>
    LDVARUINT16
    s5 s5 XCPU
    s1 s5 XCHG
    s1 s4 XCHG
    s3 s3 s0 XCHG3
  }>
  $Excesses$_store PROCINLINE:<{
    3576854235 PUSHINT
    ROT
    32 STU
    64 STU
  }>
  $Excesses$_store_cell PROCINLINE:<{
    NEWC
    SWAP
    $Excesses$_store INLINECALLDICT
    ENDC
  }>
  $GetStaticData$_load PROCINLINE:<{
    32 LDU
    SWAP
    801842850 PUSHINT
    EQUAL
    129 THROWIFNOT
    64 LDU
    SWAP
  }>
  $ReportStaticData$_store PROCINLINE:<{
    2339837749 PUSHINT
    s0 s4 XCHG2
    32 STU
    s1 s2 XCHG
    64 STU
    257 PUSHINT
    STIX
    SWAP
    __tact_store_address INLINECALLDICT
  }>
  $ReportStaticData$_store_cell PROCINLINE:<{
    NEWC
    3 -ROLL
    $ReportStaticData$_store INLINECALLDICT
    ENDC
  }>
  $ProveOwnership$_load PROCINLINE:<{
    32 LDU
    SWAP
    81711432 PUSHINT
    EQUAL
    129 THROWIFNOT
    64 LDU
    __tact_load_address INLINECALLDICT
    SWAP
    LDREF
    1 LDI
    4 -ROLL
  }>
  $RequestOwner$_load PROCINLINE:<{
    32 LDU
    SWAP
    3502489578 PUSHINT
    EQUAL
    129 THROWIFNOT
    64 LDU
    __tact_load_address INLINECALLDICT
    SWAP
    LDREF
    1 LDI
    4 -ROLL
  }>
  $OwnershipProof$_store PROCREF:<{
    86296494 PUSHINT
    s0 s7 XCHG2
    32 STU
    s1 s5 XCHG
    64 STU
    s1 s3 XCHG
    256 STU
    SWAP
    __tact_store_address INLINECALLDICT
    STREF
    64 STU
    OVER
    ISNULL
    NOT
    IF:<{
      TRUE
      SWAP
      1 STI
      STREF
    }>ELSE<{
      FALSE
      s2 POP
      1 STI
    }>
  }>
  $OwnershipProof$_store_cell PROCINLINE:<{
    NEWC
    6 -ROLL
    $OwnershipProof$_store INLINECALLDICT
    ENDC
  }>
  $OwnerInfo$_store PROCREF:<{
    232130531 PUSHINT
    s0 s8 XCHG2
    32 STU
    s1 s6 XCHG
    64 STU
    s1 s4 XCHG
    256 STU
    ROT
    __tact_store_address INLINECALLDICT
    SWAP
    __tact_store_address INLINECALLDICT
    STREF
    64 STU
    OVER
    ISNULL
    NOT
    IF:<{
      TRUE
      SWAP
      1 STI
      STREF
    }>ELSE<{
      FALSE
      s2 POP
      1 STI
    }>
  }>
  $OwnerInfo$_store_cell PROCINLINE:<{
    NEWC
    7 -ROLL
    $OwnerInfo$_store INLINECALLDICT
    ENDC
  }>
  $Revoke$_load PROCINLINE:<{
    32 LDU
    SWAP
    1871312355 PUSHINT
    EQUAL
    129 THROWIFNOT
    64 LDU
    SWAP
  }>
  $NftItem$_store PROCINLINE:<{
    s7 s6 XCHG2
    __tact_store_address INLINECALLDICT
    s1 s4 XCHG
    257 PUSHINT
    STIX
    s1 s2 XCHG
    1 STI
    SWAP
    __tact_store_address_opt INLINECALLDICT
    OVER
    ISNULL
    NOT
    IF:<{
      TRUE
      SWAP
      1 STI
      STREF
    }>ELSE<{
      FALSE
      s2 POP
      1 STI
    }>
    NEWC
    s0 s3 XCHG2
    __tact_store_address_opt INLINECALLDICT
    64 STU
    ENDC
    SWAP
    STREF
  }>
  $NftItem$_load PROCINLINE:<{
    __tact_load_address INLINECALLDICT
    SWAP
    257 PUSHINT
    LDIX
    1 LDI
    __tact_load_address_opt INLINECALLDICT
    SWAP
    1 LDI
    SWAP
    IF:<{
      LDREF
    }>ELSE<{
      PUSHNULL
      SWAP
    }>
    LDREF
    SWAP
    CTOS
    __tact_load_address_opt INLINECALLDICT
    SWAP
    64 LDU
    DROP
    s2 s7 XCHG
    s2 s6 XCHG
    s2 s5 XCHG
    s2 s4 XCHG
    s2 s3 XCHG
  }>
  $Context$_get_value PROCINLINE:<{
    s1 s3 XCHG
    3 BLKDROP
  }>
  $GetNftData$_to_external PROCINLINE:<{
  }>
  $NftItem$init$_load PROCINLINE:<{
    __tact_load_address INLINECALLDICT
    SWAP
    257 PUSHINT
    LDIX
    __tact_load_address INLINECALLDICT
    s3 s3 s0 XCHG3
  }>
  $NftItem$_contract_init PROCREF:<{
    PUSHNULL
    PUSHNULL
    0 PUSHINT
    23386 PUSHINT
    __tact_context_get_sender INLINECALLDICT
    s7 s(-1) PUXC
    __tact_slice_eq_bits INLINECALLDICT
    THROWANYIFNOT
    FALSE
    s4 s4 XCHG2
  }>
  $NftItem$_contract_load PROCREF:<{
    c4 PUSH
    CTOS
    LDREF
    SWAP
    __tact_context_sys SETGLOB
    1 LDI
    SWAP
    IFJMP:<{
      $NftItem$_load INLINECALLDICT
      1 7 BLKDROP2
    }>
    MYADDR
    11 PLDU
    10 PUSHPOW2
    EQUAL
    137 THROWIFNOT
    $NftItem$init$_load INLINECALLDICT
    s0 s3 XCHG
    ENDS
    ROT
    $NftItem$_contract_init INLINECALLDICT
  }>
  $NftItem$_contract_store PROCINLINE:<{
    NEWC
    __tact_context_sys GETGLOB
    SWAP
    STREF
    TRUE
    SWAP
    1 STI
    7 -ROLL
    $NftItem$_store INLINECALLDICT
    ENDC
    c4 POP
  }>
  $global_send PROCREF:<{
        NEWC
    1 PUSHINT
    SWAP
    2 STI
    s0 s7 XCHG2
    __tact_store_bool INLINECALLDICT
    0 PUSHINT
    SWAP
    3 STI
    s0 s5 XCHG2
    __tact_store_address INLINECALLDICT
    s0 s3 XCHG2
        STVARUINT16
    0 PUSHINT
    SWAP
    105 STI
    s3 PUSH
    ISNULL
    NOT
    IF:<{
      TRUE
    }>ELSE<{
      s4 PUSH
      ISNULL
      NOT
    }>
    IF:<{
      TRUE
      __tact_store_bool INLINECALLDICT
          NEWC
      FALSE
      __tact_store_bool INLINECALLDICT
      FALSE
      __tact_store_bool INLINECALLDICT
      s4 PUSH
      ISNULL
      NOT
      IF:<{
        TRUE
        __tact_store_bool INLINECALLDICT
        s0 s4 XCHG
        __tact_not_null INLINECALLDICT
        s0 s4 XCHG2
            STREF
      }>ELSE<{
        s4 POP
        s0 s3 XCHG
        FALSE
        __tact_store_bool INLINECALLDICT
      }>
      s4 PUSH
      ISNULL
      NOT
      IF:<{
        TRUE
        __tact_store_bool INLINECALLDICT
        s0 s4 XCHG
        __tact_not_null INLINECALLDICT
        s0 s4 XCHG2
            STREF
      }>ELSE<{
        s4 POP
        s0 s3 XCHG
        FALSE
        __tact_store_bool INLINECALLDICT
      }>
      FALSE
      __tact_store_bool INLINECALLDICT
      s0 s2 XCHG
      TRUE
      __tact_store_bool INLINECALLDICT
      s0 s2 XCHG
          ENDC
      ROT
          STREF
    }>ELSE<{
      s3 POP
      s3 POP
      SWAP
      FALSE
      __tact_store_bool INLINECALLDICT
    }>
    OVER
    ISNULL
    NOT
    IF:<{
      TRUE
      __tact_store_bool INLINECALLDICT
      SWAP
      __tact_not_null INLINECALLDICT
      SWAP
          STREF
    }>ELSE<{
      NIP
      FALSE
      __tact_store_bool INLINECALLDICT
    }>
        ENDC
    SWAP
        SENDMSG
  }>
  $Cell$_fun_asSlice PROCINLINE:<{
        CTOS
  }>
  $NftItem$_fun_msgValue PROCREF:<{
        BALANCE FIRST
    OVER
    SUB
    20000000 PUSHINT
    TUCK
        MIN
    SUB
    20000000 PUSHINT
    ADD
    SUB
  }>
  $GetNftData$_constructor_is_initialized_index_collection_address_owner_address_individual_content PROCINLINE:<{
  }>
  $NftItem$_fun_get_nft_data PROCREF:<{
    __tact_string_builder_start_string INLINECALLDICT
    s3 PUSH
    __tact_not_null INLINECALLDICT
    $Cell$_fun_asSlice INLINECALLDICT
    __tact_string_builder_append INLINECALLDICT
        B{b5ee9c7241010101000b0000126974656d2e6a736f6e1baa5832} B>boc <s PUSHSLICE
    __tact_string_builder_append INLINECALLDICT
    s4 PUSH
    __tact_not_null INLINECALLDICT
    SWAP
    __tact_string_builder_end INLINECALLDICT
    s6 PUSH
    s8 s2 s(-1) PUXC2
    s10 PUSH
    -ROT
    $GetNftData$_constructor_is_initialized_index_collection_address_owner_address_individual_content INLINECALLDICT
  }>
  $NftItem$_fun_get_authority_address PROCREF:<{
    OVER
  }>
  $NftItem$_fun_get_revoked_time PROCREF:<{
    DUP
  }>
  $SendParameters$_constructor_to_value_mode_body PROCINLINE:<{
    TRUE
    4 -ROLL
    PUSHNULL
    PUSHNULL
  }>
  $Excesses$_constructor_query_id PROCINLINE:<{
  }>
  $SendParameters$_constructor_to_value_mode_bounce_body PROCINLINE:<{
    s1 s4 XCHG
    s3 s3 s0 XCHG3
    PUSHNULL
    PUSHNULL
  }>
  $ReportStaticData$_constructor_query_id_index_id_collection PROCINLINE:<{
  }>
  $OwnershipProof$_constructor_query_id_item_id_owner_data_revoked_at_content PROCINLINE:<{
  }>
  $OwnerInfo$_constructor_query_id_item_id_initiator_owner_data_revoked_at_content PROCINLINE:<{
  }>
  $SendParameters$_constructor_to_bounce_value_mode PROCINLINE:<{
    s2 s3 XCHG
    PUSHNULL
    PUSHNULL
    PUSHNULL
  }>
  $NftItem$_internal_binary_Transfer PROCINLINE:<{
    s2 s3 XCHG
    3 BLKDROP
    __tact_context_get INLINECALLDICT
    DROP
    s2 POP
    s7 s11 XCHG
    s6 s10 XCHG
    s5 s9 XCHG
    s4 s8 XCHG
    s3 s11 XCHG
    s10 s9 s9 XCHG3
    $NftItem$_fun_msgValue INLINECALLDICT
    s5 PUSH
    0 EQINT
    IF:<{
      3 3 BLKDROP2
      s4 PUSH
      17481 PUSHINT
      s0 s8 XCHG
      __tact_slice_eq_bits INLINECALLDICT
      s1 s7 XCHG
      THROWANYIFNOT
      TRUE
      s8 s6 PUSH2
      0 GTINT
      IF:<{
        1 PUSHINT
        s0 s7 XCHG
        $Excesses$_constructor_query_id INLINECALLDICT
        $Excesses$_store_cell INLINECALLDICT
        s3 s10 XCHG
        s8 s7 s0 XCHG3
        $SendParameters$_constructor_to_value_mode_body INLINECALLDICT
        $global_send INLINECALLDICT
        DROP
      }>ELSE<{
        s2 s9 XCHG
        s6 POP
        s6 POP
        DROP
      }>
    }>ELSE<{
      DROP
      s7 POP
      s7 POP
      s7 POP
      s7 POP
      9397 PUSHINT
      THROWANY
      s3 s6 XCHG
      s0 s4 XCHG
    }>
    s6 s0 s5 XCHG3
    s0 s2 XCHG
  }>
  $NftItem$_internal_binary_GetStaticData PROCINLINE:<{
    __tact_context_get_sender INLINECALLDICT
    0 PUSHINT
    64 PUSHINT
    TRUE
    s4 s10 s11 XCPU2
    $ReportStaticData$_constructor_query_id_index_id_collection INLINECALLDICT
    $ReportStaticData$_store_cell INLINECALLDICT
    s3 s4 XCHG
    s1 s3 s0 XCHG3
    $SendParameters$_constructor_to_value_mode_bounce_body INLINECALLDICT
    $global_send INLINECALLDICT
    DROP
  }>
  $NftItem$_internal_binary_ProveOwnership PROCINLINE:<{
    14534 PUSHINT
    __tact_context_get_sender INLINECALLDICT
    s9 s(-1) PUXC
    __tact_slice_eq_bits_nullable_one INLINECALLDICT
    THROWANYIFNOT
    0 PUSHINT
    64 PUSHINT
    s9 PUSH
    __tact_not_null INLINECALLDICT
    s0 s3 XCHG
    IF:<{
      s8 PUSH
    }>ELSE<{
      PUSHNULL
    }>
    s12 PUSH
    s4 s7 XCHG
    s7 s5 s3 XCHG3
    s8 s5 PUXC
    $OwnershipProof$_constructor_query_id_item_id_owner_data_revoked_at_content INLINECALLDICT
    $OwnershipProof$_store_cell INLINECALLDICT
    s1 s3 s0 XCHG3
    $SendParameters$_constructor_to_value_mode_body INLINECALLDICT
    $global_send INLINECALLDICT
    DROP
  }>
  $NftItem$_internal_binary_RequestOwner PROCINLINE:<{
    0 PUSHINT
    64 PUSHINT
    __tact_context_get_sender INLINECALLDICT
    s10 PUSH
    __tact_not_null INLINECALLDICT
    s0 s4 XCHG
    IF:<{
      s9 PUSH
    }>ELSE<{
      PUSHNULL
    }>
    s13 PUSH
    s5 s8 XCHG
    s3 s1 s4 XCHG3
    s8 s6 s0 XCHG3
    s9 s6 PUXC
    $OwnerInfo$_constructor_query_id_item_id_initiator_owner_data_revoked_at_content INLINECALLDICT
    $OwnerInfo$_store_cell INLINECALLDICT
    s1 s3 s0 XCHG3
    $SendParameters$_constructor_to_value_mode_body INLINECALLDICT
    $global_send INLINECALLDICT
    DROP
  }>
  $NftItem$_internal_text_986c2ba124bb9287eb4a0bd8d3104e1c0067a3c93952d889c74d08185bd30d4d PROCINLINE:<{
    2DROP
    14534 PUSHINT
    __tact_context_get_sender INLINECALLDICT
    s1 s3 XCHG
    __tact_slice_eq_bits_nullable_one INLINECALLDICT
    s1 s2 XCHG
    THROWANYIFNOT
    PUSHNULL
    PUSHNULL
    0 PUSHINT
        BALANCE FIRST
    __tact_context_get INLINECALLDICT
    $Context$_get_value INLINECALLDICT
    SUB
    20000000 PUSHINT
    SUB
    DUP
    0 GTINT
    IF:<{
      __tact_context_get_sender INLINECALLDICT
      FALSE
      ROT
      66 PUSHINT
      $SendParameters$_constructor_to_bounce_value_mode INLINECALLDICT
      $global_send INLINECALLDICT
      DROP
    }>ELSE<{
      DROP
    }>
    s2 s3 XCHG
  }>
  $NftItem$_internal_binary_Revoke PROCINLINE:<{
    DROP
    42435 PUSHINT
    __tact_context_get_sender INLINECALLDICT
    s3 s(-1) PUXC
    __tact_slice_eq_bits_nullable_one INLINECALLDICT
    THROWANYIFNOT
    10990 PUSHINT
    SWAP
    0 EQINT
    THROWANYIFNOT
        NOW
  }>
  %get_nft_data PROC:<{
    $NftItem$_contract_load INLINECALLDICT
    $NftItem$_fun_get_nft_data INLINECALLDICT
    7 5 BLKDROP2
    $GetNftData$_to_external INLINECALLDICT
  }>
  %get_authority_address PROC:<{
    $NftItem$_contract_load INLINECALLDICT
    $NftItem$_fun_get_authority_address INLINECALLDICT
    7 1 BLKDROP2
  }>
  %get_revoked_time PROC:<{
    $NftItem$_contract_load INLINECALLDICT
    $NftItem$_fun_get_revoked_time INLINECALLDICT
    7 1 BLKDROP2
  }>
  lazy_deployment_completed PROC:<{
    c4 PUSH
    CTOS
    1 LDI
    SWAP
  }>
  $NftItem$_contract_router_internal PROCREF:<{
    c2 SAVE
    SAMEALTSAVE
    SWAP
    IFJMP:<{
      DROP
      TRUE
    }>
    0 PUSHINT
    OVER
    SBITS
    31 GTINT
    IF:<{
      DROP
      DUP
      32 PLDU
    }>
    DUP
    1607220500 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      $Transfer$_load INLINECALLDICT
      1 6 BLKDROP2
      $NftItem$_internal_binary_Transfer INLINECALLDICT
      TRUE
    }>
    DUP
    801842850 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      $GetStaticData$_load INLINECALLDICT
      NIP
      $NftItem$_internal_binary_GetStaticData INLINECALLDICT
      TRUE
    }>
    DUP
    81711432 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      $ProveOwnership$_load INLINECALLDICT
      1 4 BLKDROP2
      $NftItem$_internal_binary_ProveOwnership INLINECALLDICT
      TRUE
    }>
    DUP
    3502489578 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      $RequestOwner$_load INLINECALLDICT
      1 4 BLKDROP2
      $NftItem$_internal_binary_RequestOwner INLINECALLDICT
      TRUE
    }>
    DUP
    1871312355 PUSHINT
    EQUAL
    IFJMP:<{
      DROP
      $Revoke$_load INLINECALLDICT
      NIP
      $NftItem$_internal_binary_Revoke INLINECALLDICT
      TRUE
    }>
    0 EQINT
    IF:<{
      HASHSU
      68942673587165287250091431766268285359585855031594722650534428464957340454221 PUSHINT
      EQUAL
      IFJMP:<{
        $NftItem$_internal_text_986c2ba124bb9287eb4a0bd8d3104e1c0067a3c93952d889c74d08185bd30d4d INLINECALLDICT
        TRUE
        RETALT
      }>
    }>ELSE<{
      DROP
    }>
    FALSE
  }>
  recv_internal PROC:<{
    SWAP
    CTOS
    4 LDU
    SWAP
    1 PUSHINT
    AND
    NEGATE
    SWAP
    LDMSGADDR
    SWAP
    __tact_verify_address INLINECALLDICT
    s0 s4 s2 PUXCPU
    s0 s3 XCHG
    4 TUPLE
    __tact_context SETGLOB
    s0 s2 XCHG
    __tact_context_sender SETGLOB
    $NftItem$_contract_load INLINECALLDICT
    2 7 BLKSWAP
    $NftItem$_contract_router_internal INLINECALLDICT
    130 THROWIFNOT
    $NftItem$_contract_store INLINECALLDICT
  }>
}END>c
