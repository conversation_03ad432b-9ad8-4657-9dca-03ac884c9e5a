import { Collection } from "@/core.constants";
import { firebaseStorage, firestore } from "@/root-context";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
} from "firebase/firestore";
import {
  deleteObject,
  getDownloadURL,
  ref,
  uploadBytes,
} from "firebase/storage";

const COLLECTION_NAME = "collections";
const STORAGE_FOLDER = "collections";

export const createCollection = async (
  collectionData: Omit<Collection, "id" | "createdAt" | "updatedAt">
) => {
  try {
    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...collectionData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  } catch (error) {
    console.error("Error creating collection:", error);
    throw error;
  }
};

export const updateCollection = async (id: string, collectionData: Partial<Collection>) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...collectionData,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error("Error updating collection:", error);
    throw error;
  }
};

export const deleteCollection = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error("Error deleting collection:", error);
    throw error;
  }
};

export const getCollections = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("createdAt", "desc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("createdAt", "desc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const collections: Collection[] = [];

    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as Collection);
    });

    return {
      collections,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching collections:", error);
    throw error;
  }
};

export const uploadCollectionLogo = async (
  file: File,
  collectionId?: string
): Promise<string> => {
  try {
    const fileName = `${Date.now()}_${file.name}`;
    const filePath = collectionId
      ? `${STORAGE_FOLDER}/${collectionId}/${fileName}`
      : `${STORAGE_FOLDER}/temp/${fileName}`;
    
    const storageRef = ref(firebaseStorage, filePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return downloadURL;
  } catch (error) {
    console.error("Error uploading collection logo:", error);
    throw error;
  }
};

export const deleteCollectionLogo = async (logoUrl: string): Promise<void> => {
  try {
    if (logoUrl && logoUrl.includes("firebasestorage.googleapis.com")) {
      const storageRef = ref(firebaseStorage, logoUrl);
      await deleteObject(storageRef);
    }
  } catch (error) {
    console.error("Error deleting collection logo:", error);
    // Don't throw error for logo deletion as it's not critical
  }
};
