;;
;; Header files for NftCollection
;; NOTE: declarations are sorted for optimal order
;;

;; __tact_verify_address
slice __tact_verify_address(slice address) impure inline;

;; __tact_load_address
(slice, slice) __tact_load_address(slice cs) inline;

;; __tact_store_address
builder __tact_store_address(builder b, slice address) inline;

;; __tact_store_address_opt
builder __tact_store_address_opt(builder b, slice address) inline;

;; __tact_create_address
slice __tact_create_address(int chain, int hash) inline;

;; __tact_compute_contract_address
slice __tact_compute_contract_address(int chain, cell code, cell data) inline;

;; __tact_not_null
forall X -> X __tact_not_null(X x) impure inline;

;; __tact_context_get
(int, slice, int, slice) __tact_context_get() inline;

;; __tact_context_get_sender
slice __tact_context_get_sender() inline;

;; __tact_store_bool
builder __tact_store_bool(builder b, int v) inline;

;; __tact_slice_eq_bits
int __tact_slice_eq_bits(slice a, slice b) inline;

;; __tact_dict_set_code
cell __tact_dict_set_code(cell dict, int id, cell code) inline;

;; __tact_dict_get_code
cell __tact_dict_get_code(cell dict, int id) inline;

;; __tact_string_builder_start
tuple __tact_string_builder_start(builder b) inline;

;; __tact_string_builder_start_string
tuple __tact_string_builder_start_string() inline;

;; __tact_string_builder_end
cell __tact_string_builder_end(tuple builders) inline;

;; __tact_string_builder_append
((tuple), ()) __tact_string_builder_append(tuple builders, slice sc) inline_ref;

;; $LogEventMintRecord$_store
builder $LogEventMintRecord$_store(builder build_0, (slice, int, int) v) inline;

;; $LogEventMintRecord$_store_cell
cell $LogEventMintRecord$_store_cell((slice, int, int) v) inline;

;; $Transfer$_store
builder $Transfer$_store(builder build_0, (int, slice, slice, cell, int, slice) v) inline_ref;

;; $Transfer$_store_cell
cell $Transfer$_store_cell((int, slice, slice, cell, int, slice) v) inline;

;; $NftCollection$_store
builder $NftCollection$_store(builder build_0, (int, slice, cell, int, slice) v) inline;

;; $NftCollection$_load
(slice, ((int, slice, cell, int, slice))) $NftCollection$_load(slice sc_0) inline;

;; $StateInit$_to_external
(cell, cell) $StateInit$_to_external(((cell, cell)) v) inline;

;; $Context$_get_value
_ $Context$_get_value((int, slice, int, slice) v) inline;

;; $CollectionData$_to_external
(int, cell, slice) $CollectionData$_to_external(((int, cell, slice)) v) inline;

;; $NftCollection$init$_load
(slice, ((slice, cell, int))) $NftCollection$init$_load(slice sc_0) inline;

;; $NftItem$init$_store
builder $NftItem$init$_store(builder build_0, (slice, int, slice) v) inline;

;; $NftCollection$_contract_init
(int, slice, cell, int, slice) $NftCollection$_contract_init(slice $owner_address, cell $collection_content, int $nft_price) impure inline_ref;

;; $NftCollection$_contract_load
(int, slice, cell, int, slice) $NftCollection$_contract_load() impure inline_ref;

;; $NftCollection$_contract_store
() $NftCollection$_contract_store((int, slice, cell, int, slice) v) impure inline;

;; $global_emptyCell
cell $global_emptyCell() impure inline;

;; $Cell$_fun_asSlice
slice $Cell$_fun_asSlice(cell $self) impure inline;

;; $global_emptySlice
slice $global_emptySlice() impure inline;

;; $global_contractAddress
slice $global_contractAddress((cell, cell) $s) impure inline;

;; $global_send
int $global_send((int, slice, int, int, cell, cell, cell) $params) impure inline_ref;

;; $global_emit
() $global_emit(cell $body) impure inline;

;; $SendParameters$_constructor_to_value_bounce_mode_body_code_data
((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_value_bounce_mode_body_code_data(slice $to, int $value, int $bounce, int $mode, cell $body, cell $code, cell $data) inline;

;; $Transfer$_constructor_query_id_new_owner_response_destination_custom_payload_forward_amount_forward_payload
((int, slice, slice, cell, int, slice)) $Transfer$_constructor_query_id_new_owner_response_destination_custom_payload_forward_amount_forward_payload(int $query_id, slice $new_owner, slice $response_destination, cell $custom_payload, int $forward_amount, slice $forward_payload) inline;

;; $NftItem$_init_child
(cell, cell) $NftItem$_init_child(cell sys', slice $collection_address, int $item_index, slice $authority_address) inline_ref;

;; $NftCollection$_fun_getNftItemInit
((int, slice, cell, int, slice), (cell, cell)) $NftCollection$_fun_getNftItemInit((int, slice, cell, int, slice) $self, int $item_index) impure inline_ref;

;; $NftCollection$_fun_mint
((int, slice, cell, int, slice), ()) $NftCollection$_fun_mint((int, slice, cell, int, slice) $self, slice $sender, int $msgValue) impure inline_ref;

;; $CollectionData$_constructor_next_item_index_collection_content_owner_address
((int, cell, slice)) $CollectionData$_constructor_next_item_index_collection_content_owner_address(int $next_item_index, cell $collection_content, slice $owner_address) inline;

;; $NftCollection$_fun_get_collection_data
((int, slice, cell, int, slice), (int, cell, slice)) $NftCollection$_fun_get_collection_data((int, slice, cell, int, slice) $self) impure inline_ref;

;; $NftCollection$_fun_get_nft_price
((int, slice, cell, int, slice), int) $NftCollection$_fun_get_nft_price((int, slice, cell, int, slice) $self) impure inline_ref;

;; $NftCollection$_fun_get_nft_mint_total_cost
((int, slice, cell, int, slice), int) $NftCollection$_fun_get_nft_mint_total_cost((int, slice, cell, int, slice) $self) impure inline_ref;

;; $NftCollection$_fun_get_nft_address_by_index
((int, slice, cell, int, slice), slice) $NftCollection$_fun_get_nft_address_by_index((int, slice, cell, int, slice) $self, int $item_index) impure inline_ref;

;; $NftCollection$_fun_get_nft_content
((int, slice, cell, int, slice), cell) $NftCollection$_fun_get_nft_content((int, slice, cell, int, slice) $self, int $index, cell $individual_content) impure inline_ref;

;; $SendParameters$_constructor_to_value_bounce_mode_body
((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_value_bounce_mode_body(slice $to, int $value, int $bounce, int $mode, cell $body) inline;

;; $LogEventMintRecord$_constructor_minter_item_id_generate_number
((slice, int, int)) $LogEventMintRecord$_constructor_minter_item_id_generate_number(slice $minter, int $item_id, int $generate_number) inline;

;; $SendParameters$_constructor_to_bounce_value_mode
((int, slice, int, int, cell, cell, cell)) $SendParameters$_constructor_to_bounce_value_mode(slice $to, int $bounce, int $value, int $mode) inline;
