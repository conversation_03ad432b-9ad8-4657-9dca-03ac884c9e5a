import { useCallback, useRef, useState } from "react";
import { DocumentSnapshot } from "firebase/firestore";

interface PaginationState<T> {
  items: T[];
  loading: boolean;
  hasMore: boolean;
  lastDoc?: DocumentSnapshot;
}

interface PaginationOptions {
  pageSize?: number;
}

export const usePaginationRequest = <T>(
  fetchFunction: (
    pageSize: number,
    lastDoc?: DocumentSnapshot
  ) => Promise<{
    items: T[];
    lastDoc?: DocumentSnapshot;
    hasMore: boolean;
  }>,
  options: PaginationOptions = {}
) => {
  const { pageSize = 10 } = options;

  const [state, setState] = useState<PaginationState<T>>({
    items: [],
    loading: false,
    hasMore: true,
  });

  const stateRef = useRef(state);
  stateRef.current = state;

  const loadItems = useCallback(
    async (reset = false) => {
      setState((prev) => ({ ...prev, loading: true }));

      try {
        const lastDoc = reset ? undefined : stateRef.current.lastDoc;
        const result = await fetchFunction(pageSize, lastDoc);

        setState((prev) => ({
          items: reset ? result.items : [...prev.items, ...result.items],
          loading: false,
          hasMore: result.hasMore,
          lastDoc: result.lastDoc,
        }));
      } catch (error) {
        console.error("Error loading items:", error);
        setState((prev) => ({ ...prev, loading: false }));
      }
    },
    [fetchFunction, pageSize]
  );

  const reset = useCallback(() => {
    setState({
      items: [],
      loading: false,
      hasMore: true,
    });
  }, []);

  return {
    items: state.items,
    loading: state.loading,
    hasMore: state.hasMore,
    loadItems,
    reset,
  };
};
