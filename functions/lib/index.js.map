{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,wCAAwC;AACxC,gDAAgD;AAIhD,KAAK,CAAC,aAAa,EAAE,CAAC;AACtB,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AAEhB,QAAA,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IAC5E,MAAM,UAAU,GAAe;QAC7B,EAAE,EAAE,IAAI,CAAC,GAAG;QACZ,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,WAAW,EAAE,IAAI,CAAC,WAAW;QAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAA+B;KACrF,CAAC;IAEF,IAAI;QACF,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;KACpD;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,UAAU,EACT,KAAa,CAAC,OAAO;YACpB,0CAA0C,CAC7C,CAAC;KACH;AACH,CAAC,CAAC,CAAC;AAEH,4CAA4C;AAC/B,QAAA,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;IAC3E,mCAAmC;IACnC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACjB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,iBAAiB,EACjB,8CAA8C,CAC/C,CAAC;KACH;IAED,IAAI;QACF,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAEzE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,WAAW,EACX,yBAAyB,CAC1B,CAAC;SACH;QAED,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;KACvB;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,UAAU,EACT,KAAa,CAAC,OAAO,IAAI,0CAA0C,CACrE,CAAC;KACH;AACH,CAAC,CAAC,CAAC"}