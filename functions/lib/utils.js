"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidEmail = exports.generateUniqueId = exports.formatDateToFirebaseTimestamp = exports.firebaseTimestampToDate = void 0;
const firestore_1 = require("firebase-admin/firestore");
/**
 * Converts a Firebase Timestamp to a JavaScript Date object
 */
function firebaseTimestampToDate(timestamp) {
    return timestamp.toDate();
}
exports.firebaseTimestampToDate = firebaseTimestampToDate;
/**
 * Formats a Firebase Timestamp or Date to a Firebase Timestamp
 */
function formatDateToFirebaseTimestamp(date) {
    if (date instanceof firestore_1.Timestamp) {
        return date;
    }
    return firestore_1.Timestamp.fromDate(date);
}
exports.formatDateToFirebaseTimestamp = formatDateToFirebaseTimestamp;
/**
 * Generates a unique ID for documents
 */
function generateUniqueId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}
exports.generateUniqueId = generateUniqueId;
/**
 * Validates email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
exports.isValidEmail = isValidEmail;
//# sourceMappingURL=utils.js.map