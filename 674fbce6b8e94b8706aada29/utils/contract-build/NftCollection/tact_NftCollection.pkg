{"name": "NftCollection", "code": "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", "abi": "{\"name\":\"NftCollection\",\"types\":[{\"name\":\"StateInit\",\"header\":null,\"fields\":[{\"name\":\"code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}]},{\"name\":\"StdAddress\",\"header\":null,\"fields\":[{\"name\":\"workchain\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":8}},{\"name\":\"address\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}}]},{\"name\":\"VarAddress\",\"header\":null,\"fields\":[{\"name\":\"workchain\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":32}},{\"name\":\"address\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"Context\",\"header\":null,\"fields\":[{\"name\":\"bounced\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"sender\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"raw\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false}}]},{\"name\":\"SendParameters\",\"header\":null,\"fields\":[{\"name\":\"bounce\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"to\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"value\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"mode\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"body\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"code\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"LogEventMintRecord\",\"header\":2743565669,\"fields\":[{\"name\":\"minter\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"item_id\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"generate_number\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}]},{\"name\":\"CollectionData\",\"header\":null,\"fields\":[{\"name\":\"next_item_index\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"collection_content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"owner_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"Transfer\",\"header\":1607220500,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"new_owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"response_destination\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":true}},{\"name\":\"custom_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"forward_amount\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":\"coins\"}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false,\"format\":\"remainder\"}}]},{\"name\":\"OwnershipAssigned\",\"header\":85167505,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"prev_owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"slice\",\"optional\":false,\"format\":\"remainder\"}}]},{\"name\":\"Excesses\",\"header\":3576854235,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"GetStaticData\",\"header\":801842850,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"ReportStaticData\",\"header\":2339837749,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"index_id\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"collection\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"GetNftData\",\"header\":null,\"fields\":[{\"name\":\"is_initialized\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"index\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"collection_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"owner_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"individual_content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}]},{\"name\":\"ProveOwnership\",\"header\":81711432,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"dest\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"with_content\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}}]},{\"name\":\"RequestOwner\",\"header\":3502489578,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"dest\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"forward_payload\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"with_content\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}}]},{\"name\":\"OwnershipProof\",\"header\":86296494,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"item_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}},{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"revoked_at\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"OwnerInfo\",\"header\":232130531,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"item_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":256}},{\"name\":\"initiator\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"data\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"revoked_at\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}},{\"name\":\"content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}}]},{\"name\":\"Revoke\",\"header\":1871312355,\"fields\":[{\"name\":\"query_id\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]},{\"name\":\"NftCollection$Data\",\"header\":null,\"fields\":[{\"name\":\"next_item_index\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":32}},{\"name\":\"owner_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"collection_content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}},{\"name\":\"nft_price\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"authority_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}}]},{\"name\":\"NftItem$Data\",\"header\":null,\"fields\":[{\"name\":\"collection_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":false}},{\"name\":\"item_index\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"is_initialized\",\"type\":{\"kind\":\"simple\",\"type\":\"bool\",\"optional\":false}},{\"name\":\"owner\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":true}},{\"name\":\"individual_content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":true}},{\"name\":\"authority_address\",\"type\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":true}},{\"name\":\"revoked_at\",\"type\":{\"kind\":\"simple\",\"type\":\"uint\",\"optional\":false,\"format\":64}}]}],\"receivers\":[{\"receiver\":\"internal\",\"message\":{\"kind\":\"text\",\"text\":\"Mint\"}},{\"receiver\":\"internal\",\"message\":{\"kind\":\"text\",\"text\":\"Withdraw\"}}],\"getters\":[{\"name\":\"get_collection_data\",\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"CollectionData\",\"optional\":false}},{\"name\":\"get_nft_price\",\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"get_nft_mint_total_cost\",\"arguments\":[],\"returnType\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"get_nft_address_by_index\",\"arguments\":[{\"name\":\"item_index\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"address\",\"optional\":true}},{\"name\":\"getNftItemInit\",\"arguments\":[{\"name\":\"item_index\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"StateInit\",\"optional\":false}},{\"name\":\"get_nft_content\",\"arguments\":[{\"name\":\"index\",\"type\":{\"kind\":\"simple\",\"type\":\"int\",\"optional\":false,\"format\":257}},{\"name\":\"individual_content\",\"type\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}],\"returnType\":{\"kind\":\"simple\",\"type\":\"cell\",\"optional\":false}}],\"errors\":{\"2\":{\"message\":\"Stack underflow\"},\"3\":{\"message\":\"Stack overflow\"},\"4\":{\"message\":\"Integer overflow\"},\"5\":{\"message\":\"Integer out of expected range\"},\"6\":{\"message\":\"Invalid opcode\"},\"7\":{\"message\":\"Type check error\"},\"8\":{\"message\":\"Cell overflow\"},\"9\":{\"message\":\"Cell underflow\"},\"10\":{\"message\":\"Dictionary error\"},\"11\":{\"message\":\"'Unknown' error\"},\"12\":{\"message\":\"Fatal error\"},\"13\":{\"message\":\"Out of gas error\"},\"14\":{\"message\":\"Virtualization error\"},\"32\":{\"message\":\"Action list is invalid\"},\"33\":{\"message\":\"Action list is too long\"},\"34\":{\"message\":\"Action is invalid or not supported\"},\"35\":{\"message\":\"Invalid source address in outbound message\"},\"36\":{\"message\":\"Invalid destination address in outbound message\"},\"37\":{\"message\":\"Not enough TON\"},\"38\":{\"message\":\"Not enough extra-currencies\"},\"39\":{\"message\":\"Outbound message does not fit into a cell after rewriting\"},\"40\":{\"message\":\"Cannot process a message\"},\"41\":{\"message\":\"Library reference is null\"},\"42\":{\"message\":\"Library change action error\"},\"43\":{\"message\":\"Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree\"},\"50\":{\"message\":\"Account state size exceeded limits\"},\"128\":{\"message\":\"Null reference exception\"},\"129\":{\"message\":\"Invalid serialization prefix\"},\"130\":{\"message\":\"Invalid incoming message\"},\"131\":{\"message\":\"Constraints error\"},\"132\":{\"message\":\"Access denied\"},\"133\":{\"message\":\"Contract stopped\"},\"134\":{\"message\":\"Invalid argument\"},\"135\":{\"message\":\"Code of a contract was not found\"},\"136\":{\"message\":\"Invalid address\"},\"137\":{\"message\":\"Masterchain support is not enabled for this contract\"},\"9397\":{\"message\":\"SBT cannot be transferred\"},\"10990\":{\"message\":\"Already revoked\"},\"14534\":{\"message\":\"Not owner\"},\"17481\":{\"message\":\"Initialized tx need from collection\"},\"23386\":{\"message\":\"Not from collection\"},\"24690\":{\"message\":\"Non-sequential NFTs\"},\"26825\":{\"message\":\"Only owner can withdraw\"},\"31066\":{\"message\":\"No TON to withdraw\"},\"41925\":{\"message\":\"Insufficient funds for minting\"},\"42435\":{\"message\":\"Not authorized\"}},\"interfaces\":[\"org.ton.introspection.v0\",\"org.ton.abi.ipfs.v0\",\"org.ton.deploy.lazy.v0\",\"org.ton.debug.v0\",\"org.ton.chain.workchain.v0\"]}", "init": {"kind": "direct", "args": [{"name": "owner_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "collection_content", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "nft_price", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "prefix": {"bits": 1, "value": 0}, "deployment": {"kind": "system-cell", "system": "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"}}, "sources": {"contracts/message.tact": "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", "contracts/nft_collection.tact": "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"}, "compiler": {"name": "tact", "version": "1.5.2", "parameters": "{\"entrypoint\":\"contracts/nft_collection.tact\",\"options\":{\"debug\":true}}"}}