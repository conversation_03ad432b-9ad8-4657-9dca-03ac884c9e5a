'use client'

import { useState, useEffect, useCallback } from 'react';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { Address } from "@ton/core";
import { Button } from '@/components/ui/button';

export default function TonWallet() {
    const [tonConnectUI] = useTonConnectUI();
    const [tonWalletAddress, setTonWalletAddress] = useState<string>('');
    const [isConnecting, setIsConnecting] = useState(false);
    const [tonBalance, setTonBalance] = useState<string>('0');

    const handleWalletConnection = useCallback(async (address: string) => {
        try {
            setTonWalletAddress(address);
            // Fetch balance
            await fetchTonBalance(address);
        } catch (error) {
            console.error('Error connecting wallet:', error);
        } finally {
            setIsConnecting(false);
        }
    }, []);

    const handleWalletDisconnection = useCallback(async () => {
        setTonWalletAddress('');
        setTonBalance('0');
    }, []);

    useEffect(() => {
        const unsubscribe = tonConnectUI.onStatusChange(async (wallet) => {
            if (wallet && isConnecting) {
                await handleWalletConnection(wallet.account.address);
            } else if (!wallet && !isConnecting) {
                await handleWalletDisconnection();
            }
        });

        return () => {
            unsubscribe();
        };
    }, [tonConnectUI, handleWalletConnection, handleWalletDisconnection, isConnecting]);

    const fetchTonBalance = async (address: string) => {
        try {
            // This is a simplified balance fetch - in a real app you'd use TON API
            // For now, we'll just show a placeholder
            setTonBalance('0.00');
        } catch (error) {
            console.error('Error fetching TON balance:', error);
            setTonBalance('0.00');
        }
    };

    const handleWalletAction = async () => {
        if (tonConnectUI.account?.address) {
            await tonConnectUI.disconnect();
        } else {
            setIsConnecting(true);
            await tonConnectUI.openModal();
        }
    };

    const formatAddress = (address: string) => {
        if (!address) return '';
        try {
            const addr = Address.parse(address);
            const formatted = addr.toString();
            return `${formatted.slice(0, 6)}...${formatted.slice(-6)}`;
        } catch {
            return `${address.slice(0, 6)}...${address.slice(-6)}`;
        }
    };

    return (
        <div className="flex flex-col items-center space-y-4 p-6 border rounded-lg">
            <h2 className="text-xl font-semibold">TON Wallet</h2>
            
            {tonWalletAddress ? (
                <div className="text-center space-y-2">
                    <div className="text-sm text-muted-foreground">Connected Wallet:</div>
                    <div className="font-mono text-sm bg-muted px-3 py-1 rounded">
                        {formatAddress(tonWalletAddress)}
                    </div>
                    <div className="text-sm text-muted-foreground">Balance:</div>
                    <div className="text-lg font-semibold">
                        {tonBalance} TON
                    </div>
                </div>
            ) : (
                <div className="text-center text-muted-foreground">
                    No wallet connected
                </div>
            )}

            <Button 
                onClick={handleWalletAction}
                disabled={isConnecting}
                className="w-full"
            >
                {isConnecting 
                    ? 'Connecting...' 
                    : tonWalletAddress 
                        ? 'Disconnect Wallet' 
                        : 'Connect TON Wallet'
                }
            </Button>
        </div>
    );
}
