/* eslint-disable @typescript-eslint/no-require-imports */
"use client";

import "firebaseui/dist/firebaseui.css";

import { getAuth, GoogleAuthProvider } from "firebase/auth";
import type * as firebaseui from "firebaseui";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { useIsClient } from "@/hooks/use-is-client";
import { useRootContext } from "@/root-context";

export const AuthPage = () => {
  const { currentUser } = useRootContext();
  const router = useRouter();
  const [ui, setUi] = useState<firebaseui.auth.AuthUI | null>(null);
  const isClient = useIsClient();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [firebaseui, setFirebaseui] = useState<any>(null);

  useEffect(() => {
    if (isClient) {
      if (typeof window !== "undefined") {
        const firebaseui = require("firebaseui");
        setFirebaseui(firebaseui);
        require("firebaseui/dist/firebaseui.css");
      }
    }
  }, [isClient]);

  useEffect(() => {
    // Check if FirebaseUI instance exists to avoid duplication
    if (!ui && isClient && firebaseui) {
      const uiConfig: firebaseui.auth.Config = {
        signInOptions: [
          // Allow both Email/Password sign-in and Google sign-in
          {
            provider: GoogleAuthProvider.PROVIDER_ID,
            signInMethod: GoogleAuthProvider.GOOGLE_SIGN_IN_METHOD,
            customParameters: {
              prompt: "select_account", // Forces to select an account
            },
          },
        ],
        signInSuccessUrl: "/admin", // URL to redirect to after successful login or signup
        credentialHelper: firebaseui?.auth?.CredentialHelper.NONE, // Disable account chooser
        tosUrl: "/terms-of-service", // Terms of service URL
        privacyPolicyUrl: "/privacy-policy", // Privacy policy URL
        callbacks: {
          signInFailure: (error) => {
            console.error(error);
          },
          signInSuccessWithAuthResult: () => {
            router.replace("/admin"); // Navigate to the admin page after success
            return false; // Prevent redirect as we're handling it manually
          },
        },
        // This shows both "Sign In" and "Sign Up" options in the same form.
        signInFlow: "popup", // Can also use 'redirect' if you prefer the redirect flow
      };

      const firebaseUiWidget = new firebaseui.auth.AuthUI(getAuth());
      firebaseUiWidget.start("#firebaseui-auth-container", uiConfig);
      setUi(firebaseUiWidget);
    }

    return () => {
      if (ui) {
        ui.reset();
      }
    };
  }, [firebaseui, isClient, router, ui]);

  // If the user is already signed in, redirect them to the admin page
  useEffect(() => {
    if (currentUser) {
      router.replace("/admin");
    }
  }, [currentUser, router]);

  if (!isClient) {
    return null; // Render nothing on the server
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-400/50 via-green-500/50 to-purple-500/50">
      <div className="w-[400px] rounded-lg bg-white bg-opacity-90 p-8 shadow-lg">
        <h1 className="mb-4 text-center text-2xl font-bold text-black">
          Admin Authorization
        </h1>
        <div
          id="firebaseui-auth-container"
          style={{
            width: "100%",
          }}
        ></div>
      </div>
    </div>
  );
};
