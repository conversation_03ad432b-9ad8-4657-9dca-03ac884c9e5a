PROGRAM{
  DECLPROC __tact_verify_address
  DECLPROC __tact_load_address
  DECLPROC __tact_store_address
  DECLPROC __tact_store_address_opt
  DECLPROC __tact_create_address
  DECLPROC __tact_compute_contract_address
  DECLPROC __tact_not_null
  DECLPROC __tact_context_get
  DECLPROC __tact_context_get_sender
  DECLPROC __tact_store_bool
  DECLPROC __tact_slice_eq_bits
  DECLPROC __tact_dict_set_code
  DECLPROC __tact_dict_get_code
  DECLPROC __tact_string_builder_start
  DECLPROC __tact_string_builder_start_string
  DECLPROC __tact_string_builder_end
  DECLPROC __tact_string_builder_append
  DECLPROC $LogEventMintRecord$_store
  DECLPROC $LogEventMintRecord$_store_cell
  DECLPROC $Transfer$_store
  DECLPROC $Transfer$_store_cell
  DECLPROC $NftCollection$_store
  DECLPROC $NftCollection$_load
  DECLPROC $StateInit$_to_external
  DECLPROC $Context$_get_value
  DECLPROC $CollectionData$_to_external
  DECLPROC $NftCollection$init$_load
  DECLPROC $NftItem$init$_store
  DECLPROC $NftCollection$_contract_init
  DECLPROC $NftCollection$_contract_load
  DECLPROC $NftCollection$_contract_store
  DECLPROC $global_emptyCell
  DECLPROC $Cell$_fun_asSlice
  DECLPROC $global_emptySlice
  DECLPROC $global_contractAddress
  DECLPROC $global_send
  DECLPROC $global_emit
  DECLPROC $SendParameters$_constructor_to_value_bounce_mode_body_code_data
  DECLPROC $Transfer$_constructor_query_id_new_owner_response_destination_custom_payload_forward_amount_forward_payload
  DECLPROC $NftItem$_init_child
  DECLPROC $NftCollection$_fun_getNftItemInit
  DECLPROC $NftCollection$_fun_mint
  DECLPROC $CollectionData$_constructor_next_item_index_collection_content_owner_address
  DECLPROC $NftCollection$_fun_get_collection_data
  DECLPROC $NftCollection$_fun_get_nft_price
  DECLPROC $NftCollection$_fun_get_nft_mint_total_cost
  DECLPROC $NftCollection$_fun_get_nft_address_by_index
  DECLPROC $NftCollection$_fun_get_nft_content
  DECLPROC $SendParameters$_constructor_to_value_bounce_mode_body
  DECLPROC $LogEventMintRecord$_constructor_minter_item_id_generate_number
  DECLPROC $SendParameters$_constructor_to_bounce_value_mode
  DECLPROC $NftCollection$_internal_text_247c7bd5f39e2258d80ac36a0419a1ab5779757825a6cc0e915368f00610a18a
  DECLPROC $NftCollection$_internal_text_250b76e2b9576fc6b4c45129483006b0003a0c39b6f7ae413d177f4e3479dbca
  102491 DECLMETHOD %get_collection_data
  129299 DECLMETHOD %get_nft_price
  65652 DECLMETHOD %get_nft_mint_total_cost
  92067 DECLMETHOD %get_nft_address_by_index
  81078 DECLMETHOD %getNftItemInit
  68445 DECLMETHOD %get_nft_content
  115390 DECLMETHOD lazy_deployment_completed
  DECLPROC $NftCollection$_contract_router_internal
  DECLPROC recv_internal
  DECLGLOBVAR __tact_context
  DECLGLOBVAR __tact_context_sender
  DECLGLOBVAR __tact_context_sys
  DECLGLOBVAR __tact_randomized
  __tact_verify_address PROCINLINE:<{
    DUP
    SBITS
    267 PUSHINT
    EQUAL
    136 THROWIFNOT
    DUP
    11 PLDU
    DUP
    1279 PUSHINT
    EQUAL
    137 THROWIF
    10 PUSHPOW2
    EQUAL
    136 THROWIFNOT
  }>
  __tact_load_address PROCINLINE:<{
    LDMSGADDR
    SWAP
    __tact_verify_address INLINECALLDICT
  }>
  __tact_store_address PROCINLINE:<{
    __tact_verify_address INLINECALLDICT
    STSLICER
  }>
  __tact_store_address_opt PROCINLINE:<{
    DUP
    ISNULL
    IF:<{
      DROP
      0 PUSHINT
      SWAP
      2 STU
    }>ELSE<{
      __tact_store_address INLINECALLDICT
    }>
  }>
  __tact_create_address PROCINLINE:<{
    NEWC
    2 PUSHINT
    SWAP
    2 STU
    0 PUSHINT
    SWAP
    1 STU
    s1 s2 XCHG
    8 STI
    256 STU
    ENDC
    CTOS
    __tact_verify_address INLINECALLDICT
  }>
  __tact_compute_contract_address PROCINLINE:<{
    NEWC
    0 PUSHINT
    SWAP
    2 STU
    3 PUSHINT
    SWAP
    2 STU
    0 PUSHINT
    SWAP
    1 STU
    s1 s2 XCHG
    STREF
    STREF
    ENDC
    HASHCU
    __tact_create_address INLINECALLDICT
  }>
  __tact_not_null PROCINLINE:<{
    DUP
    ISNULL
    128 THROWIF
  }>
  __tact_context_get PROCINLINE:<{
    __tact_context GETGLOB
    4 UNTUPLE
  }>
  __tact_context_get_sender PROCINLINE:<{
    __tact_context_sender GETGLOB
  }>
  __tact_store_bool PROCINLINE:<{
    SWAP
    1 STI
  }>
  __tact_slice_eq_bits PROCINLINE:<{
    SDEQ
  }>
  __tact_dict_set_code PROCINLINE:<{
    s0 s2 XCHG
    16 PUSHINT
    DICTUSETREF
  }>
  __tact_dict_get_code PROCINLINE:<{
    SWAP
    16 PUSHINT
    DICTUGETREF
    NULLSWAPIFNOT
    135 THROWIFNOT
  }>
  __tact_string_builder_start PROCINLINE:<{
    NIL
    SWAP
    TPUSH
    PUSHNULL
    TPUSH
  }>
  __tact_string_builder_start_string PROCINLINE:<{
    NEWC
    __tact_string_builder_start INLINECALLDICT
  }>
  __tact_string_builder_end PROCINLINE:<{
    UNCONS
    SWAP
    ENDC
    WHILE:<{
      OVER
      ISNULL
      NOT
    }>DO<{
      SWAP
      UNCONS
      -ROT
      STREF
      ENDC
    }>
    NIP
  }>
  __tact_string_builder_append PROCREF:<{
    DUP
    SREFS
    OVER
    SBITS
    WHILE:<{
      DUP
      0 GTINT
      s2 PUSH
      0 GTINT
      OR
    }>DO<{
      s0 s3 XCHG
      UNCONS
      127 PUSHINT
      s2 PUSH
      BBITS
      3 RSHIFT#
      SUB
      s0 s5 XCHG
      3 RSHIFT#
      s5 s5 XCPU
      MIN
      DUP
      0 GTINT
      IF:<{
        DUP
        3 LSHIFT#
        s1 s5 XCHG
        LDSLICEX
        s3 s3 XCHG2
        STSLICER
        s0 s1 s4 XCHG3
      }>
      -ROT
      CONS
      s4 s1 PUSH2
      SUB
      0 GTINT
      IF:<{
        NEWC
        SWAP
        CONS
        s4 s4 XCHG2
        SUB
        3 LSHIFT#
      }>ELSE<{
        NIP
        s3 POP
        0 GTINT
        IF:<{
          LDREF
          DROP
          CTOS
          DUP
          SREFS
          OVER
          SBITS
        }>ELSE<{
          0 PUSHINT
          DUP
        }>
      }>
    }>
    3 BLKDROP
  }>
  $LogEventMintRecord$_store PROCINLINE:<{
    2743565669 PUSHINT
    s0 s4 XCHG2
    32 STU
    ROT
    __tact_store_address INLINECALLDICT
    257 PUSHINT
    STIX
    257 PUSHINT
    STIX
  }>
  $LogEventMintRecord$_store_cell PROCINLINE:<{
    NEWC
    3 -ROLL
    $LogEventMintRecord$_store INLINECALLDICT
    ENDC
  }>
  $Transfer$_store PROCREF:<{
    1607220500 PUSHINT
    s0 s7 XCHG2
    32 STU
    s1 s5 XCHG
    64 STU
    s0 s3 XCHG2
    __tact_store_address INLINECALLDICT
    SWAP
    __tact_store_address_opt INLINECALLDICT
    OVER
    ISNULL
    NOT
    IF:<{
      TRUE
      SWAP
      1 STI
      STREF
    }>ELSE<{
      FALSE
      s2 POP
      1 STI
    }>
    SWAP
    STVARUINT16
    SWAP
    STSLICER
  }>
  $Transfer$_store_cell PROCINLINE:<{
    NEWC
    6 -ROLL
    $Transfer$_store INLINECALLDICT
    ENDC
  }>
  $NftCollection$_store PROCINLINE:<{
    s4 s5 XCHG2
    32 STU
    ROT
    __tact_store_address INLINECALLDICT
    STREF
    257 PUSHINT
    STIX
    SWAP
    __tact_store_address INLINECALLDICT
  }>
  $NftCollection$_load PROCINLINE:<{
    32 LDU
    __tact_load_address INLINECALLDICT
    SWAP
    LDREF
    257 PUSHINT
    LDIX
    __tact_load_address INLINECALLDICT
    s1 s5 XCHG
    s1 s4 XCHG
    s3 s3 s0 XCHG3
  }>
  $StateInit$_to_external PROCINLINE:<{
  }>
  $Context$_get_value PROCINLINE:<{
    s1 s3 XCHG
    3 BLKDROP
  }>
  $CollectionData$_to_external PROCINLINE:<{
  }>
  $NftCollection$init$_load PROCINLINE:<{
    __tact_load_address INLINECALLDICT
    SWAP
    LDREF
    257 PUSHINT
    LDIX
    3 -ROLL
  }>
  $NftItem$init$_store PROCINLINE:<{
    2SWAP
    __tact_store_address INLINECALLDICT
    s1 s2 XCHG
    257 PUSHINT
    STIX
    SWAP
    __tact_store_address INLINECALLDICT
  }>
  $NftCollection$_contract_init PROCREF:<{
    s2 PUSH
    -ROT
    0 PUSHINT
    s0 s4 XCHG
  }>
  $NftCollection$_contract_load PROCREF:<{
    c4 PUSH
    CTOS
    LDREF
    SWAP
    __tact_context_sys SETGLOB
    1 LDI
    SWAP
    IFJMP:<{
      $NftCollection$_load INLINECALLDICT
      1 5 BLKDROP2
    }>
    MYADDR
    11 PLDU
    10 PUSHPOW2
    EQUAL
    137 THROWIFNOT
    $NftCollection$init$_load INLINECALLDICT
    s0 s3 XCHG
    ENDS
    ROT
    $NftCollection$_contract_init INLINECALLDICT
  }>
  $NftCollection$_contract_store PROCINLINE:<{
    NEWC
    __tact_context_sys GETGLOB
    SWAP
    STREF
    TRUE
    SWAP
    1 STI
    5 -ROLL
    $NftCollection$_store INLINECALLDICT
    ENDC
    c4 POP
  }>
  $global_emptyCell PROCINLINE:<{
        NEWC
        ENDC
  }>
  $Cell$_fun_asSlice PROCINLINE:<{
        CTOS
  }>
  $global_emptySlice PROCINLINE:<{
    $global_emptyCell INLINECALLDICT
    $Cell$_fun_asSlice INLINECALLDICT
  }>
  $global_contractAddress PROCINLINE:<{
    0 PUSHINT
    -ROT
    __tact_compute_contract_address INLINECALLDICT
  }>
  $global_send PROCREF:<{
        NEWC
    1 PUSHINT
    SWAP
    2 STI
    s0 s7 XCHG2
    __tact_store_bool INLINECALLDICT
    0 PUSHINT
    SWAP
    3 STI
    s0 s5 XCHG2
    __tact_store_address INLINECALLDICT
    s0 s3 XCHG2
        STVARUINT16
    0 PUSHINT
    SWAP
    105 STI
    s3 PUSH
    ISNULL
    NOT
    IF:<{
      TRUE
    }>ELSE<{
      s4 PUSH
      ISNULL
      NOT
    }>
    IF:<{
      TRUE
      __tact_store_bool INLINECALLDICT
          NEWC
      FALSE
      __tact_store_bool INLINECALLDICT
      FALSE
      __tact_store_bool INLINECALLDICT
      s4 PUSH
      ISNULL
      NOT
      IF:<{
        TRUE
        __tact_store_bool INLINECALLDICT
        s0 s4 XCHG
        __tact_not_null INLINECALLDICT
        s0 s4 XCHG2
            STREF
      }>ELSE<{
        s4 POP
        s0 s3 XCHG
        FALSE
        __tact_store_bool INLINECALLDICT
      }>
      s4 PUSH
      ISNULL
      NOT
      IF:<{
        TRUE
        __tact_store_bool INLINECALLDICT
        s0 s4 XCHG
        __tact_not_null INLINECALLDICT
        s0 s4 XCHG2
            STREF
      }>ELSE<{
        s4 POP
        s0 s3 XCHG
        FALSE
        __tact_store_bool INLINECALLDICT
      }>
      FALSE
      __tact_store_bool INLINECALLDICT
      s0 s2 XCHG
      TRUE
      __tact_store_bool INLINECALLDICT
      s0 s2 XCHG
          ENDC
      ROT
          STREF
    }>ELSE<{
      s3 POP
      s3 POP
      SWAP
      FALSE
      __tact_store_bool INLINECALLDICT
    }>
    OVER
    ISNULL
    NOT
    IF:<{
      TRUE
      __tact_store_bool INLINECALLDICT
      SWAP
      __tact_not_null INLINECALLDICT
      SWAP
          STREF
    }>ELSE<{
      NIP
      FALSE
      __tact_store_bool INLINECALLDICT
    }>
        ENDC
    SWAP
        SENDMSG
  }>
  $global_emit PROCINLINE:<{
        NEWC
    15211807202738752817960438464513 PUSHINT
    SWAP
    104 STU
        STREF
        ENDC
    0 PUSHINT
        SENDRAWMSG
  }>
  $SendParameters$_constructor_to_value_bounce_mode_body_code_data PROCINLINE:<{
    s4 s6 XCHG
    s4 s5 XCHG
  }>
  $Transfer$_constructor_query_id_new_owner_response_destination_custom_payload_forward_amount_forward_payload PROCINLINE:<{
  }>
  $NftItem$_init_child PROCREF:<{
    s0 s3 XCHG
    CTOS
    LDDICT
    DROP
    NEWDICT
    SWAP
    31210 PUSHINT
    __tact_dict_get_code INLINECALLDICT
    SWAP
    31210 PUSHINT
    s2 PUSH
    __tact_dict_set_code INLINECALLDICT
    NEWC
    SWAP
    NEWC
    STDICT
    ENDC
    SWAP
    STREF
    FALSE
    SWAP
    1 STI
    3 -ROLL
    s0 s4 XCHG
    $NftItem$init$_store INLINECALLDICT
    ENDC
  }>
  $NftCollection$_fun_getNftItemInit PROCREF:<{
    __tact_context_sys GETGLOB
        MYADDR
    s0 s2 s3 XC2PU
    $NftItem$_init_child INLINECALLDICT
  }>
  $NftCollection$_fun_mint PROCREF:<{
    24690 PUSHINT
    s7 PUSH
    -1 GTINT
    THROWANYIFNOT
    s6 PUSH
    s0 s5 XCHG
    s4 s6 XCHG
    s6 s3 s3 XCHG3
    s0 s7 XCHG
    $NftCollection$_fun_getNftItemInit INLINECALLDICT
    2DUP
    $global_contractAddress INLINECALLDICT
    FALSE
    2 PUSHINT
    0 PUSHINT
    DUP
    $global_emptySlice INLINECALLDICT
    s11 PUSH
    s3 s4 XCHG
    s3 s14 XCHG
    s11 s2 s(-1) PUXC2
    $Transfer$_constructor_query_id_new_owner_response_destination_custom_payload_forward_amount_forward_payload INLINECALLDICT
    $Transfer$_store_cell INLINECALLDICT
    s2 s6 XCHG
    s5 s11 XCHG
    s1 s4 XCHG
    s3 s10 XCHG
    s0 s1 s10 XCHG3
    $SendParameters$_constructor_to_value_bounce_mode_body_code_data INLINECALLDICT
    $global_send INLINECALLDICT
    DROP
    s0 s2 XCHG
    INC
    s3 s4 XCHG2
  }>
  $CollectionData$_constructor_next_item_index_collection_content_owner_address PROCINLINE:<{
  }>
  $NftCollection$_fun_get_collection_data PROCREF:<{
    __tact_string_builder_start_string INLINECALLDICT
    s3 PUSH
    $Cell$_fun_asSlice INLINECALLDICT
    __tact_string_builder_append INLINECALLDICT
        B{b5ee9c7241010101000b0000126d6574612e6a736f6ebde57607} B>boc <s PUSHSLICE
    __tact_string_builder_append INLINECALLDICT
    __tact_string_builder_end INLINECALLDICT
    s5 s4 s(-1) PU2XC
    $CollectionData$_constructor_next_item_index_collection_content_owner_address INLINECALLDICT
  }>
  $NftCollection$_fun_get_nft_price PROCREF:<{
    OVER
  }>
  $NftCollection$_fun_get_nft_mint_total_cost PROCREF:<{
    40000000 PUSHINT
    s2 PUSH
    ADD
  }>
  $NftCollection$_fun_get_nft_address_by_index PROCREF:<{
    $NftCollection$_fun_getNftItemInit INLINECALLDICT
    $global_contractAddress INLINECALLDICT
  }>
  $NftCollection$_fun_get_nft_content PROCREF:<{
    NIP
    __tact_string_builder_start_string INLINECALLDICT
    SWAP
    $Cell$_fun_asSlice INLINECALLDICT
    __tact_string_builder_append INLINECALLDICT
    __tact_string_builder_end INLINECALLDICT
  }>
  $SendParameters$_constructor_to_value_bounce_mode_body PROCINLINE:<{
    s2 s4 XCHG
    s2 s3 XCHG
    PUSHNULL
    PUSHNULL
  }>
  $LogEventMintRecord$_constructor_minter_item_id_generate_number PROCINLINE:<{
  }>
  $SendParameters$_constructor_to_bounce_value_mode PROCINLINE:<{
    s2 s3 XCHG
    PUSHNULL
    PUSHNULL
    PUSHNULL
  }>
  $NftCollection$_internal_text_247c7bd5f39e2258d80ac36a0419a1ab5779757825a6cc0e915368f00610a18a PROCINLINE:<{
    __tact_context_get INLINECALLDICT
    DROP
    s2 POP
    s4 s6 XCHG
    s3 s5 XCHG
    s6 s5 s6 XCHG3
    $NftCollection$_fun_get_nft_mint_total_cost INLINECALLDICT
    s6 PUSH
    41925 PUSHINT
    s0 s2 XCHG
    GEQ
    THROWANYIFNOT
        BALANCE FIRST
    s6 PUSH
    SUB
    20000000 PUSHINT
    TUCK
        MIN
    SUB
    20000000 PUSHINT
    ADD
    s2 PUSH
    ADD
    s1 s6 XCHG
    SUB
    OVER
    5 MULCONST
    100 PUSHINT
    DIV
    s2 s0 PUSH2
    SUB
    OVER
    0 GTINT
    IF:<{
          B{b5ee9c72410101010024000043801b7aafdcab7b249690b211dcc95afd3a48a1b99a060b5f4518d2515c72f9936ad033001b23} B>boc <s PUSHSLICE
      FALSE
      2 PUSHINT
          B{b5ee9c72410101010014000024000000004e465420436f6d6d697373696f6e6714cb04} B>boc PUSHREF
      s3 s4 XCHG
      s3 s5 XCHG
      $SendParameters$_constructor_to_value_bounce_mode_body INLINECALLDICT
      $global_send INLINECALLDICT
      DROP
    }>ELSE<{
      NIP
    }>
    DUP
    0 GTINT
    IF:<{
      FALSE
      2 PUSHINT
          B{b5ee9c7241010101000e000018000000004e46542053616c653d34d2cb} B>boc PUSHREF
      s7 PUSH
      4 -ROLL
      $SendParameters$_constructor_to_value_bounce_mode_body INLINECALLDICT
      $global_send INLINECALLDICT
      DROP
    }>ELSE<{
      DROP
    }>
    s4 s6 XCHG
    s3 s5 XCHG
    s4 s3 s0 XCHG3
    s1 s2 XCHG
    $NftCollection$_fun_mint INLINECALLDICT
    __tact_context_get_sender INLINECALLDICT
        RANDU256
    s6 s(-1) PUXC
    $LogEventMintRecord$_constructor_minter_item_id_generate_number INLINECALLDICT
    $LogEventMintRecord$_store_cell INLINECALLDICT
    $global_emit INLINECALLDICT
  }>
  $NftCollection$_internal_text_250b76e2b9576fc6b4c45129483006b0003a0c39b6f7ae413d177f4e3479dbca PROCINLINE:<{
    26825 PUSHINT
    __tact_context_get_sender INLINECALLDICT
    s5 s(-1) PUXC
    __tact_slice_eq_bits INLINECALLDICT
    THROWANYIFNOT
        BALANCE FIRST
    __tact_context_get INLINECALLDICT
    $Context$_get_value INLINECALLDICT
    SUB
    20000000 PUSHINT
    SUB
    31066 PUSHINT
    OVER
    0 GTINT
    THROWANYIFNOT
    __tact_context_get_sender INLINECALLDICT
    TRUE
    ROT
    66 PUSHINT
    $SendParameters$_constructor_to_bounce_value_mode INLINECALLDICT
    $global_send INLINECALLDICT
    DROP
  }>
  %get_collection_data PROC:<{
    $NftCollection$_contract_load INLINECALLDICT
    $NftCollection$_fun_get_collection_data INLINECALLDICT
    5 3 BLKDROP2
    $CollectionData$_to_external INLINECALLDICT
  }>
  %get_nft_price PROC:<{
    $NftCollection$_contract_load INLINECALLDICT
    $NftCollection$_fun_get_nft_price INLINECALLDICT
    5 1 BLKDROP2
  }>
  %get_nft_mint_total_cost PROC:<{
    $NftCollection$_contract_load INLINECALLDICT
    $NftCollection$_fun_get_nft_mint_total_cost INLINECALLDICT
    5 1 BLKDROP2
  }>
  %get_nft_address_by_index PROC:<{
    $NftCollection$_contract_load INLINECALLDICT
    5 ROLL
    $NftCollection$_fun_get_nft_address_by_index INLINECALLDICT
    5 1 BLKDROP2
  }>
  %getNftItemInit PROC:<{
    $NftCollection$_contract_load INLINECALLDICT
    5 ROLL
    $NftCollection$_fun_getNftItemInit INLINECALLDICT
    5 2 BLKDROP2
    $StateInit$_to_external INLINECALLDICT
  }>
  %get_nft_content PROC:<{
    $NftCollection$_contract_load INLINECALLDICT
    2 5 BLKSWAP
    $NftCollection$_fun_get_nft_content INLINECALLDICT
    5 1 BLKDROP2
  }>
  lazy_deployment_completed PROC:<{
    c4 PUSH
    CTOS
    1 LDI
    SWAP
  }>
  $NftCollection$_contract_router_internal PROCREF:<{
    c2 SAVE
    SAMEALTSAVE
    SWAP
    IFJMP:<{
      DROP
      TRUE
    }>
    0 PUSHINT
    OVER
    SBITS
    31 GTINT
    IF:<{
      DROP
      DUP
      32 PLDU
    }>
    0 EQINT
    IF:<{
      HASHSU
      DUP
      16503206267955344479789506433990479957112383025105740036956182953240829141386 PUSHINT
      EQUAL
      IFJMP:<{
        DROP
        $NftCollection$_internal_text_247c7bd5f39e2258d80ac36a0419a1ab5779757825a6cc0e915368f00610a18a INLINECALLDICT
        TRUE
        RETALT
      }>
      16755831233829029960236183814598340968099579476287469518633057839499026488266 PUSHINT
      EQUAL
      IFJMP:<{
        $NftCollection$_internal_text_250b76e2b9576fc6b4c45129483006b0003a0c39b6f7ae413d177f4e3479dbca INLINECALLDICT
        TRUE
        RETALT
      }>
    }>ELSE<{
      DROP
    }>
    FALSE
  }>
  recv_internal PROC:<{
    SWAP
    CTOS
    4 LDU
    SWAP
    1 PUSHINT
    AND
    NEGATE
    SWAP
    LDMSGADDR
    SWAP
    __tact_verify_address INLINECALLDICT
    s0 s4 s2 PUXCPU
    s0 s3 XCHG
    4 TUPLE
    __tact_context SETGLOB
    s0 s2 XCHG
    __tact_context_sender SETGLOB
    $NftCollection$_contract_load INLINECALLDICT
    2 5 BLKSWAP
    $NftCollection$_contract_router_internal INLINECALLDICT
    130 THROWIFNOT
    $NftCollection$_contract_store INLINECALLDICT
  }>
}END>c
