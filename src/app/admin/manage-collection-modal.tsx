"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Image from "next/image";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Collection, CollectionStatus, COLLECTION_STATUS_TEXT } from "@/core.constants";
import {
  createCollection,
  updateCollection,
  uploadCollectionLogo,
} from "@/api/collection-api";

const collectionSchema = z.object({
  collectionId: z.string().min(1, "Collection ID is required"),
  name: z.string().min(1, "Name is required"),
  logoUrl: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  status: z.nativeEnum(CollectionStatus),
});

type CollectionFormData = z.infer<typeof collectionSchema>;

interface ManageCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  collection: Collection | null;
  onSave: () => void;
}

export const ManageCollectionModal = ({
  isOpen,
  onClose,
  collection,
  onSave,
}: ManageCollectionModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>("");

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CollectionFormData>({
    resolver: zodResolver(collectionSchema),
    defaultValues: {
      collectionId: "",
      name: "",
      logoUrl: "",
      description: "",
      status: CollectionStatus.PRELAUNCH,
    },
  });

  const watchedLogoUrl = watch("logoUrl");

  useEffect(() => {
    if (collection) {
      reset({
        collectionId: collection.collectionId,
        name: collection.name,
        logoUrl: collection.logoUrl,
        description: collection.description,
        status: collection.status,
      });
      setLogoPreview(collection.logoUrl);
    } else {
      reset({
        collectionId: "",
        name: "",
        logoUrl: "",
        description: "",
        status: CollectionStatus.PRELAUNCH,
      });
      setLogoPreview("");
    }
    setLogoFile(null);
  }, [collection, reset]);

  const handleLogoUpload = async (file: File) => {
    setUploadingLogo(true);
    try {
      const logoUrl = await uploadCollectionLogo(file, collection?.id);
      setValue("logoUrl", logoUrl);
      setLogoPreview(logoUrl);
    } catch (error) {
      console.error("Error uploading logo:", error);
    } finally {
      setUploadingLogo(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: CollectionFormData) => {
    setIsSubmitting(true);
    try {
      // Upload logo if a new file was selected
      if (logoFile) {
        const logoUrl = await uploadCollectionLogo(logoFile, collection?.id);
        data.logoUrl = logoUrl;
      }

      if (collection) {
        await updateCollection(collection.id, data);
      } else {
        await createCollection(data);
      }

      onSave();
      onClose();
    } catch (error) {
      console.error("Error saving collection:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {collection ? "Edit Collection" : "Add Collection"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="collectionId">Collection ID</Label>
            <Input
              id="collectionId"
              {...register("collectionId")}
              placeholder="Enter collection ID"
            />
            {errors.collectionId && (
              <p className="text-sm text-red-600">{errors.collectionId.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter collection name"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="logo">Logo</Label>
            <div className="flex items-center gap-4">
              {logoPreview && (
                <Image
                  src={logoPreview}
                  alt="Logo preview"
                  width={64}
                  height={64}
                  className="w-16 h-16 object-contain rounded border"
                />
              )}
              <div className="flex-1">
                <Input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  disabled={uploadingLogo}
                />
                {uploadingLogo && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Uploading...
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter collection description"
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={watch("status")}
              onValueChange={(value) => setValue("status", value as CollectionStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(CollectionStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {COLLECTION_STATUS_TEXT[status]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || uploadingLogo}>
              {isSubmitting ? "Saving..." : collection ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
