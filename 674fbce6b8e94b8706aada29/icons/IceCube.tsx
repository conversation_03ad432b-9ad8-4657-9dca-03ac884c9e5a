import { IconProps } from "../utils/types";


const IceCube: React.FC<IconProps> = ({ size = 24, className = "" }) => {

    const svgSize = `${size}px`;

    // Color variables
    const s0 = "#0488cf";
    const s1 = "#ffffff";
    const s2 = "#abddf8";
    const s3 = "#fffeff";
    const s4 = "#e1f3fd";
    const s5 = "#9cd7f9";
    const s6 = "#fdfeff";

    return (
        <svg version="1.2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3600 3600" className={className} height={svgSize} width={svgSize}>
	<g id="Layer 1">
		<g id="&lt;Group&gt;">
			<g id="&lt;Group&gt;">
				<g id="&lt;Group&gt;">
					<g id="&lt;Group&gt;">
						<g id="&lt;Group&gt;">
							<path id="&lt;Path&gt;" fill={s0} d="m3254.6 1051.6c-0.1-50.7-36-112.9-79.8-138.3l-1292.2-751c-43.7-25.4-115.4-25.5-159.2-0.2l-1302.8 752.2c-43.9 25.3-79.6 87.4-79.5 138l4.3 1496.2c0.1 50.6 36 112.8 79.8 138.2l1292.2 751c43.7 25.4 115.4 25.5 159.2 0.2l1302.8-752.2c43.9-25.3 79.6-87.4 79.5-138z"/>
						</g>
						<g id="&lt;Group&gt;">
							<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1797.3 3514.3c-40.3 0-79-9.6-108.8-26.9l-1292.2-750.9c-61.6-35.9-108.2-116.6-108.4-187.9l-4.3-1496.1c-0.2-71.5 46.3-152.3 108.2-188l1302.8-752.2c29.7-17.2 68.1-26.6 108.1-26.6 40.3 0 79 9.6 108.8 26.9l1292.2 750.9c61.6 35.9 108.3 116.6 108.5 187.9l4.2 1496.1c0.2 71.5-46.3 152.3-108.2 188l-1302.8 752.2c-29.7 17.2-68.1 26.6-108.1 26.6zm5.4-3313.6c-19.5 0-38.4 4.2-50.6 11.2l-1302.8 752.2c-26.1 15.1-50.7 57.9-50.7 88.1l4.3 1496.1c0.1 30.3 25 73.5 51.2 88.7l1292.2 750.9c12.2 7.1 31.3 11.4 51 11.4 19.5 0 38.4-4.2 50.6-11.2l1302.8-752.2c26.1-15.1 50.7-57.9 50.7-88.1l-4.3-1496.1c-0.1-30.3-25-73.4-51.2-88.7l-1292.2-750.9c-12.2-7.1-31.2-11.4-51-11.4z"/>
						</g>
					</g>
					<g id="&lt;Group&gt;">
						<g id="&lt;Group&gt;">
							<g id="&lt;Group&gt;">
								<g id="&lt;Group&gt;">
									<path id="&lt;Path&gt;" fill={s1} d="m1792.8 2033.8c-0.3-126.6-90.1-282.1-199.5-345.7l-1053.5-612.2c-109.4-63.6-198.6-12.1-198.3 114.5l3.5 1220c0.3 126.6 90.1 282.1 199.6 345.7l1053.4 612.2c109.4 63.6 198.6 12.1 198.3-114.5z"/>
									<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1686.7 3401.3q0 0 0 0c-28.8 0-59.6-9.4-91.6-28l-1053.4-612.2c-111.3-64.7-202.1-222-202.5-350.7l-3.4-1220c-0.2-50.5 13.5-91 39.6-117.2 19.8-19.8 46-30.3 75.7-30.3 28.8 0 59.6 9.4 91.6 28l1053.4 612.2c111.3 64.7 202.1 222 202.5 350.7l3.4 1220c0.2 50.5-13.5 91.1-39.6 117.2-19.8 19.8-46 30.3-75.7 30.3zm-1235.6-2346.9c-26.6 0-50 9.3-67.5 27-23.9 23.9-36.5 61.6-36.3 109l3.4 1220c0.4 125 88.6 277.9 196.7 340.7l1053.5 612.2c30.2 17.6 59.1 26.5 85.8 26.5q0 0 0 0c26.6 0 50-9.3 67.5-26.9 23.9-24 36.5-61.7 36.3-109.1l-3.4-1220c-0.4-125.1-88.6-277.9-196.7-340.7l-1053.5-612.2c-30.2-17.6-59.1-26.5-85.8-26.5z"/>
								</g>
								<g id="&lt;Group&gt;">
									<path id="&lt;Path&gt;" fill={s2} d="m1792.8 2033.8c-0.3-126.6-90.1-282.1-199.5-345.7l-1053.5-612.2c-109.4-63.6-198.6-12.1-198.3 114.5l3.5 1220c0.3 126.6 90.1 282.1 199.6 345.7l1053.4 612.2c109.4 63.6 198.6 12.1 198.3-114.5z"/>
									<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1686.7 3453.1q0 0 0 0c-38 0-77.6-11.8-117.6-35.1l-1053.5-612.2c-127.5-74.1-227.7-247.7-228.1-395.2l-3.5-1220c-0.2-64.6 18.8-117.8 54.8-153.9 29.6-29.8 68.5-45.6 112.3-45.6 38 0 77.6 11.8 117.6 35.1l1053.5 612.2c127.5 74.1 227.7 247.7 228.1 395.2l3.5 1220.1c0.2 64.5-18.8 117.8-54.8 153.9-29.6 29.7-68.5 45.5-112.3 45.5zm-1235.6-2346.9c-12.9 0-23 3.8-30.9 11.7-13.7 13.8-21.3 39.5-21.2 72.3l3.5 1220c0.3 106.9 78.6 242.5 171 296.2l1053.4 612.2c21.9 12.7 42.6 19.4 59.8 19.4 12.9 0 23-3.8 30.9-11.7 13.7-13.8 21.2-39.5 21.2-72.3l-3.5-1220c-0.3-106.9-78.6-242.5-170.9-296.1l-1053.5-612.2c-21.9-12.8-42.6-19.5-59.8-19.5z"/>
								</g>
							</g>
							<g id="&lt;Group&gt;">
								<g id="&lt;Group&gt;">
									<path id="&lt;Path&gt;" fill={s1} d="m3055.1 1074.6c109.6-63.3 109.8-167.1 0.3-230.7l-1053.4-612.2c-109.4-63.6-288.6-63.8-398.2-0.6l-1063.7 614.2c-109.6 63.2-109.7 167-0.3 230.6l1053.5 612.2c109.4 63.6 288.5 63.8 398.1 0.6z"/>
									<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1793 1741.7q0 0 0 0c-76.7 0-148.7-17.3-202.6-48.6l-1053.5-612.2c-54.8-31.8-84.9-74.6-84.8-120.4 0-45.8 30.3-88.5 85.1-120.2l1063.7-614.1c53.7-31 125.2-48.1 201.4-48.1 76.7 0 148.6 17.3 202.6 48.6l1053.4 612.2c54.8 31.9 84.9 74.6 84.9 120.5-0.1 45.8-30.3 88.5-85.2 120.1l-1063.7 614.2c-53.7 30.9-125.2 48-201.3 48zm9.3-1552.1c-74.2 0-143.7 16.5-195.6 46.5l-1063.7 614.2c-51.2 29.5-79.4 68.6-79.4 110.2-0.1 41.6 28 80.8 79.1 110.4l1053.4 612.2c52.3 30.4 122.2 47.1 196.9 47.1q0 0 0 0c74.1 0 143.6-16.5 195.5-46.5l1063.7-614.1c51.2-29.5 79.4-68.7 79.5-110.3 0-41.5-28.1-80.7-79.1-110.4l-1053.5-612.2c-52.2-30.4-122.1-47.1-196.8-47.1z"/>
								</g>
								<g id="&lt;Group&gt;">
									<path id="&lt;Path&gt;" fill={s2} d="m3055.1 1074.6c109.6-63.3 109.8-167.1 0.3-230.7l-1053.4-612.2c-109.4-63.6-288.6-63.8-398.2-0.6l-1063.7 614.2c-109.6 63.2-109.7 167-0.3 230.6l1053.5 612.2c109.4 63.6 288.5 63.8 398.1 0.6z"/>
									<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1793 1793.5q0 0 0 0c-85.7 0-166.9-19.8-228.6-55.6l-1053.5-612.2c-70.4-40.9-110.7-101.2-110.6-165.3 0.1-64.1 40.6-124.2 111.1-164.9l1063.7-614.2c61.4-35.4 142.1-54.9 227.2-54.9 85.7 0 166.9 19.7 228.6 55.6l1053.4 612.2c70.4 40.9 110.7 101.1 110.6 165.2-0.1 64.2-40.5 124.3-111 165l-1063.7 614.1c-61.4 35.4-142.1 55-227.2 55zm9.3-1552.1c-64.3 0-126.2 14.4-169.7 39.6l-1063.7 614.1c-34 19.6-53.5 43.5-53.5 65.5-0.1 22 19.4 45.9 53.3 65.6l1053.5 612.2c43.8 25.4 106 40 170.8 40q0 0 0 0c64.2 0 126.1-14.4 169.7-39.5l1063.7-614.1c33.9-19.7 53.5-43.5 53.5-65.5 0-22-19.4-45.9-53.4-65.7l-1053.4-612.1c-43.8-25.5-106.1-40.1-170.8-40.1z"/>
								</g>
							</g>
							<g id="&lt;Group&gt;">
								<g id="&lt;Group&gt;">
									<path id="&lt;Path&gt;" fill={s1} d="m1991.4 1688.7c-109.6 63.2-198.9 218.5-198.6 345.1l3.5 1220c0.3 126.6 90.3 178.3 199.9 115.1l1063.7-614.2c109.6-63.2 198.9-218.5 198.6-345.1l-3.5-1220c-0.3-126.5-90.3-178.3-199.9-115z"/>
									<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1907.6 3401.6q0 0 0 0c-70.9 0-116.8-58-117.1-147.8l-3.4-1220c-0.4-128.7 90-285.7 201.4-350.1l1063.7-614.1c32-18.4 62.7-27.8 91.5-27.8 70.9 0 116.8 58 117.1 147.8l3.4 1220c0.4 128.7-90 285.8-201.4 350.1l-1063.7 614.1c-32 18.5-62.7 27.8-91.5 27.8zm1236.1-2348.3c-26.7 0-55.5 8.8-85.7 26.2l-1063.7 614.2c-108.3 62.5-196.1 215-195.7 340.1l3.4 1220c0.3 84.1 40.7 136.3 105.6 136.3q0 0 0 0c26.7 0 55.5-8.8 85.7-26.2l1063.7-614.1c108.3-62.6 196.1-215.2 195.7-340.2l-3.4-1220c-0.3-84.1-40.7-136.3-105.6-136.3z"/>
								</g>
								<g id="&lt;Group&gt;">
									<path id="&lt;Path&gt;" fill={s2} d="m1991.4 1688.7c-109.6 63.2-198.9 218.5-198.6 345.1l3.5 1220c0.3 126.6 90.3 178.3 199.9 115.1l1063.7-614.2c109.6-63.2 198.9-218.5 198.6-345.1l-3.5-1220c-0.3-126.5-90.3-178.3-199.9-115z"/>
									<path id="&lt;Compound Path&gt;" fillRule="evenodd" fill={s0} d="m1907.6 3453.4q0 0 0 0c-100.7 0-168.5-80.2-168.8-199.4l-3.5-1220c-0.4-147.7 99.4-321.3 227.4-395.1l1063.7-614.1c39.9-23.1 79.3-34.8 117.3-34.8 100.7 0 168.5 80.2 168.8 199.4l3.5 1220.1c0.4 147.6-99.4 321.2-227.4 395l-1063.7 614.2c-39.8 23-79.3 34.7-117.3 34.7zm1236.1-2348.3c-17.3 0-38 6.6-59.8 19.3l-1063.7 614.1c-92.4 53.3-170.2 188.5-169.9 295.1l3.5 1220.1c0.1 25.4 5.5 84.6 53.8 84.6q0 0 0 0c17.3 0 38-6.6 59.8-19.3l1063.7-614.1c92.4-53.3 170.2-188.5 169.9-295.1l-3.5-1220c-0.1-25.5-5.5-84.7-53.8-84.7z"/>
								</g>
							</g>
						</g>
					</g>
				</g>
				<g id="&lt;Group&gt;">
					<path id="&lt;Path&gt;" fill={s3} d="m514.4 2475.2c-8.8-7.3-15-25.8-16.1-47.2-4.2-81.1-0.2-174.7-8.7-261.5-18.3-185.6-13-361.9 2.5-546.9 6.8-81.3-50.2-270.9 57.7-311.9 49.2-18.6 141.4 38 184.4 56.2 79.4 33.5 160.7 64.5 235.1 108.5 40.9 24.2 224.2 126.9 71.9 130.3-124.6 2.7-251.1-64.4-368.4 3.8-88.2 51.4-108.6 152.1-118.3 244.7-12.4 118-21.9 236.7-8.5 355.1 8.8 78.6 37.1 173.9 0 249.2-11.5 23.2-22.8 27.1-31.6 19.7z"/>
				</g>
				<path id="&lt;Path&gt;" fill={s4} d="m1737.4 496.5c-50.6-24-120-31.1-204.4-14.6-178.8 34.9-358.5 196.2-497 308.8-194.5 158.1-227.7 229-41.2 393.8 130.6 115.4 277.5 199.4 435.9 270.8 156.3 70.4 314.1 131.9 481.8 65.5 159-63 344.5-95.6 490.1-187.2 47.6-30 276.5-185.9 129.3-244.6-50.8-20.3-167.6 19.1-215.9 41-217 98-556.9 417.3-802.3 207.7-212.6-181.5 107.5-358.3 210.7-469.7 147.5-159.1 134.4-313.9 13-371.5z"/>
				<path id="&lt;Path&gt;" fill={s4} d="m2475.5 642.6c84 44.8 153.1 101.8 242.6 137.5 87.2 34.8 325.8 126.7 172.4 237.5-31.5 22.8-155.4 125-167.2 26.3-3.5-29.3 78.6-64.9 74.2-107.9-3.5-34.6-44.5-48.5-71.2-62.6-92.8-49.3-200.5-83.9-287.4-142.5-21.4-14.4-70.5-51.2-61.4-84.7 10.5-38.1 52.1-28.1 98-3.6z"/>
				<path id="&lt;Path&gt;" fill={s5} d="m1717.6 3326.3c13.7-13.8 21.2-39.5 21.2-72.3l-3.5-1220c-0.2-67.6-31.6-146.6-78-209.2-17.4 6.4-34 13.8-49.1 22.5l-1063.7 614.1c-63.3 36.6-89.9 86.7-80 134.8 29.8 46.4 68 86.3 109 110.2l1053.4 612.2c1 0.5 1.9 0.9 2.8 1.4 16.3 7 33.5 12.8 51.5 17.5 1.8 0.2 3.7 0.5 5.5 0.5 12.9 0 23-3.8 30.9-11.7z"/>
				<path id="&lt;Path&gt;" fill={s5} d="m1853.8 3253.7c0.1 25.4 5.5 84.6 53.8 84.6q0 0 0 0c1.3 0 2.8-0.3 4.2-0.3 19.8-5.1 38.7-11.5 56.4-19.4l1062.9-613.7c40.7-23.5 78.6-62.9 108.2-108.7 10.6-48.4-15.7-99.1-79.4-136.1l-1053.5-612.2c-21.9-12.8-46.7-22.8-73.1-30.5-49.4 63.5-83.2 146-83 216.2z"/>
				<path id="&lt;Path&gt;" fill={s6} d="m3081.2 1439.4c69.9 16.1 51.7 132.9 49.9 182-3.5 94.7 7.3 188.5 15.7 282.8 6 68.9 9.3 138.2 7.4 207.4-2.1 78-9.3 145.1-24.8 220.8-52.4 255.7-272.4 383.9-485.3 490.8-89.8 45.1-179.3 91.1-268.8 136.7-29.6 15-151 93-185 52.3-14.7-17.6 243.6-153 267.1-164.6 63.6-31.4 128.1-60.8 191.6-92.4 126.3-62.7 249.4-140.3 333.4-256.9 153.2-212.6 12.5-510.4 3.4-745.8-2.9-73.5 0.5-172.5 25-242.5 7.6-21.5 21-62.1 46.6-69.4 8.9-2.5 16.8-2.8 23.8-1.2z"/>
			</g>
		</g>
	</g>
</svg>
    );
};

export default IceCube;