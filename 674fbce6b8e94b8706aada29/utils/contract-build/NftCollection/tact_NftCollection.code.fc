#pragma version =0.4.4;
#pragma allow-post-modification;
#pragma compute-asm-ltr;

#include "tact_NftCollection.headers.fc";
#include "tact_NftCollection.stdlib.fc";
#include "tact_NftCollection.constants.fc";
#include "tact_NftCollection.storage.fc";

;;
;; Contract NftCollection functions
;;

(int, slice, cell, int, slice) $NftCollection$_contract_init(slice $owner_address, cell $collection_content, int $nft_price) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = (0, null(), null(), null(), null());
    $self'owner_address = $owner_address;
    $self'collection_content = $collection_content;
    $self'nft_price = $nft_price;
    $self'authority_address = $owner_address;
    return ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address);
}

((int, slice, cell, int, slice), (cell, cell)) $NftCollection$_fun_getNftItemInit((int, slice, cell, int, slice) $self, int $item_index) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    var $fresh$ret_4 = $NftItem$_init_child(__tact_context_sys, $global_myAddress(), $item_index, $self'authority_address);
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), $fresh$ret_4);
}

((int, slice, cell, int, slice), ()) $NftCollection$_fun_mint((int, slice, cell, int, slice) $self, slice $sender, int $msgValue) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    throw_unless(24690, ($self'next_item_index >= 0));
    var ($nft_init'code, $nft_init'data) = ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)~$NftCollection$_fun_getNftItemInit($self'next_item_index);
    $global_send($SendParameters$_constructor_to_value_bounce_mode_body_code_data($global_contractAddress(($nft_init'code, $nft_init'data)), $msgValue, false, 2, $Transfer$_store_cell($Transfer$_constructor_query_id_new_owner_response_destination_custom_payload_forward_amount_forward_payload(0, $sender, $self'owner_address, $self'collection_content, 0, $global_emptySlice())), $nft_init'code, $nft_init'data));
    $self'next_item_index = ($self'next_item_index + 1);
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), ());
}

((int, slice, cell, int, slice), (int, cell, slice)) $NftCollection$_fun_get_collection_data((int, slice, cell, int, slice) $self) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    tuple $b = __tact_string_builder_start_string();
    slice $collectionDataString = __tact_slice_to_str($Cell$_fun_asSlice($self'collection_content));
    $b~__tact_string_builder_append($collectionDataString);
    $b~__tact_string_builder_append(__gen_slice_string_7ca4179185a84c879806ec30a1f5fa692451a7fb8dfcf32a967a2cb475822481());
    var $fresh$ret_0 = $CollectionData$_constructor_next_item_index_collection_content_owner_address($self'next_item_index, __tact_string_builder_end($b), $self'owner_address);
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), $fresh$ret_0);
}

((int, slice, cell, int, slice), int) $NftCollection$_fun_get_nft_price((int, slice, cell, int, slice) $self) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    var $fresh$ret_1 = $self'nft_price;
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), $fresh$ret_1);
}

((int, slice, cell, int, slice), int) $NftCollection$_fun_get_nft_mint_total_cost((int, slice, cell, int, slice) $self) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    var $fresh$ret_2 = (40000000 + $self'nft_price);
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), $fresh$ret_2);
}

((int, slice, cell, int, slice), slice) $NftCollection$_fun_get_nft_address_by_index((int, slice, cell, int, slice) $self, int $item_index) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    var ($initCode'code, $initCode'data) = ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)~$NftCollection$_fun_getNftItemInit($item_index);
    var $fresh$ret_3 = $global_contractAddress(($initCode'code, $initCode'data));
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), $fresh$ret_3);
}

((int, slice, cell, int, slice), cell) $NftCollection$_fun_get_nft_content((int, slice, cell, int, slice) $self, int $index, cell $individual_content) impure inline_ref {
    var (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)) = $self;
    tuple $b = __tact_string_builder_start_string();
    slice $ic = __tact_slice_to_str($Cell$_fun_asSlice($individual_content));
    $b~__tact_string_builder_append($ic);
    var $fresh$ret_5 = __tact_string_builder_end($b);
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), $fresh$ret_5);
}

;;
;; Receivers of a Contract NftCollection
;;

((int, slice, cell, int, slice), ()) $NftCollection$_internal_text_247c7bd5f39e2258d80ac36a0419a1ab5779757825a6cc0e915368f00610a18a((int, slice, cell, int, slice) $self) impure inline {
    var ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address) = $self;
    var ($ctx'bounced, $ctx'sender, $ctx'value, $ctx'raw) = __tact_context_get();
    int $msgValue = $ctx'value;
    int $totalCost = ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)~$NftCollection$_fun_get_nft_mint_total_cost();
    throw_unless(41925, ($msgValue >= $totalCost));
    int $tonBalanceBeforeMsg = ($global_myBalance() - $msgValue);
    int $storageFee = (20000000 - $global_min($tonBalanceBeforeMsg, 20000000));
    $msgValue = ($msgValue - (($storageFee + 20000000) + $self'nft_price));
    int $commission = (($self'nft_price * 5) / 100);
    int $ownerPayment = ($self'nft_price - $commission);
    if (($commission > 0)) {
        $global_send($SendParameters$_constructor_to_value_bounce_mode_body(__gen_slice_address_4e7fbd773592fcdd4068a1681abfcdbf3e3429aacd88c408c4768fb915fc43c7(), $commission, false, 2, __gen_cell_comment_7bdb7cf95162364be082f66862ac670ff57e039929148fcb990d53b4fe60cea9()));
    }
    if (($ownerPayment > 0)) {
        $global_send($SendParameters$_constructor_to_value_bounce_mode_body($self'owner_address, $ownerPayment, false, 2, __gen_cell_comment_048a60bd611ab7e8887a065af586ff202a0a1d264d348daae89d1f6daf8ccd74()));
    }
    ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address)~$NftCollection$_fun_mint($ctx'sender, $msgValue);
    $global_emit($LogEventMintRecord$_store_cell($LogEventMintRecord$_constructor_minter_item_id_generate_number(__tact_context_get_sender(), $self'next_item_index, $global_nativeRandom())));
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), ());
}

((int, slice, cell, int, slice), ()) $NftCollection$_internal_text_250b76e2b9576fc6b4c45129483006b0003a0c39b6f7ae413d177f4e3479dbca((int, slice, cell, int, slice) $self) impure inline {
    var ($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address) = $self;
    throw_unless(26825, ( __tact_slice_eq_bits($self'owner_address, __tact_context_get_sender()) ));
    int $withdrawAmount = (($global_myBalance() - $Context$_get_value(__tact_context_get())) - 20000000);
    throw_unless(31066, ($withdrawAmount > 0));
    $global_send($SendParameters$_constructor_to_bounce_value_mode(__tact_context_get_sender(), true, $withdrawAmount, 66));
    return (($self'next_item_index, $self'owner_address, $self'collection_content, $self'nft_price, $self'authority_address), ());
}

;;
;; Get methods of a Contract NftCollection
;;

_ %get_collection_data() method_id(102491) {
    var self = $NftCollection$_contract_load();
    var res = self~$NftCollection$_fun_get_collection_data();
    return $CollectionData$_to_external(res);
}

_ %get_nft_price() method_id(129299) {
    var self = $NftCollection$_contract_load();
    var res = self~$NftCollection$_fun_get_nft_price();
    return res;
}

_ %get_nft_mint_total_cost() method_id(65652) {
    var self = $NftCollection$_contract_load();
    var res = self~$NftCollection$_fun_get_nft_mint_total_cost();
    return res;
}

_ %get_nft_address_by_index(int $item_index) method_id(92067) {
    int $item_index = $item_index;
    var self = $NftCollection$_contract_load();
    var res = self~$NftCollection$_fun_get_nft_address_by_index($item_index);
    return res;
}

_ %getNftItemInit(int $item_index) method_id(81078) {
    int $item_index = $item_index;
    var self = $NftCollection$_contract_load();
    var res = self~$NftCollection$_fun_getNftItemInit($item_index);
    return $StateInit$_to_external(res);
}

_ %get_nft_content(int $index, cell $individual_content) method_id(68445) {
    int $index = $index;
    cell $individual_content = $individual_content;
    var self = $NftCollection$_contract_load();
    var res = self~$NftCollection$_fun_get_nft_content($index, $individual_content);
    return res;
}

_ lazy_deployment_completed() method_id {
    return get_data().begin_parse().load_int(1);
}

;;
;; Routing of a Contract NftCollection
;;

((int, slice, cell, int, slice), int) $NftCollection$_contract_router_internal((int, slice, cell, int, slice) self, int msg_bounced, slice in_msg) impure inline_ref {
    ;; Handle bounced messages
    if (msg_bounced) {
        return (self, true);
    }
    
    ;; Parse incoming message
    int op = 0;
    if (slice_bits(in_msg) >= 32) {
        op = in_msg.preload_uint(32);
    }
    
    
    ;; Text Receivers
    if (op == 0) {
        var text_op = slice_hash(in_msg);
        
        ;; Receive "Mint" message
        if (text_op == 0x247c7bd5f39e2258d80ac36a0419a1ab5779757825a6cc0e915368f00610a18a) {
            self~$NftCollection$_internal_text_247c7bd5f39e2258d80ac36a0419a1ab5779757825a6cc0e915368f00610a18a();
            return (self, true);
        }
        
        ;; Receive "Withdraw" message
        if (text_op == 0x250b76e2b9576fc6b4c45129483006b0003a0c39b6f7ae413d177f4e3479dbca) {
            self~$NftCollection$_internal_text_250b76e2b9576fc6b4c45129483006b0003a0c39b6f7ae413d177f4e3479dbca();
            return (self, true);
        }
    }
    
    return (self, false);
}

() recv_internal(int msg_value, cell in_msg_cell, slice in_msg) impure {
    
    ;; Context
    var cs = in_msg_cell.begin_parse();
    var msg_flags = cs~load_uint(4);
    var msg_bounced = -(msg_flags & 1);
    slice msg_sender_addr = __tact_verify_address(cs~load_msg_addr());
    __tact_context = (msg_bounced, msg_sender_addr, msg_value, cs);
    __tact_context_sender = msg_sender_addr;
    
    ;; Load contract data
    var self = $NftCollection$_contract_load();
    
    ;; Handle operation
    int handled = self~$NftCollection$_contract_router_internal(msg_bounced, in_msg);
    
    ;; Throw if not handled
    throw_unless(130, handled);
    
    ;; Persist state
    $NftCollection$_contract_store(self);
}
