import { Timestamp } from "firebase-admin/firestore";

export interface UserEntity {
  id: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role: "user" | "admin";
  createdAt: Timestamp;
}

export interface ProductEntity {
  id: string;
  title: string;
  description: string;
  price: number;
  sellerId: string;
  category: string;
  imageUrls: string[];
  status: "active" | "sold" | "inactive";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface OrderEntity {
  id: string;
  buyerId: string;
  sellerId: string;
  productId: string;
  amount: number;
  status: "pending" | "confirmed" | "completed" | "cancelled";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
