{"name": "NftItem", "types": [{"name": "StateInit", "header": null, "fields": [{"name": "code", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": false}}]}, {"name": "StdAddress", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 8}}, {"name": "address", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": null, "fields": [{"name": "workchain", "type": {"kind": "simple", "type": "int", "optional": false, "format": 32}}, {"name": "address", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "Context", "header": null, "fields": [{"name": "bounced", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "sender", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "raw", "type": {"kind": "simple", "type": "slice", "optional": false}}]}, {"name": "SendParameters", "header": null, "fields": [{"name": "bounce", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "to", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "value", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "mode", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "body", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "code", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "LogEventMintRecord", "header": 2743565669, "fields": [{"name": "minter", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "item_id", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "generate_number", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}]}, {"name": "CollectionData", "header": null, "fields": [{"name": "next_item_index", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "collection_content", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "owner_address", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "Transfer", "header": 1607220500, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "new_owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "response_destination", "type": {"kind": "simple", "type": "address", "optional": true}}, {"name": "custom_payload", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "forward_amount", "type": {"kind": "simple", "type": "uint", "optional": false, "format": "coins"}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "slice", "optional": false, "format": "remainder"}}]}, {"name": "OwnershipAssigned", "header": 85167505, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "prev_owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "slice", "optional": false, "format": "remainder"}}]}, {"name": "Excesses", "header": 3576854235, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "GetStaticData", "header": 801842850, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "ReportStaticData", "header": 2339837749, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "index_id", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "collection", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "GetNftData", "header": null, "fields": [{"name": "is_initialized", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "index", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "collection_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "owner_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "individual_content", "type": {"kind": "simple", "type": "cell", "optional": false}}]}, {"name": "ProveOwnership", "header": 81711432, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "dest", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "with_content", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "RequestOwner", "header": 3502489578, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "dest", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "forward_payload", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "with_content", "type": {"kind": "simple", "type": "bool", "optional": false}}]}, {"name": "OwnershipProof", "header": 86296494, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "item_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}, {"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "revoked_at", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "content", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "OwnerInfo", "header": 232130531, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "item_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 256}}, {"name": "initiator", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "owner", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "data", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "revoked_at", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}, {"name": "content", "type": {"kind": "simple", "type": "cell", "optional": true}}]}, {"name": "Revoke", "header": 1871312355, "fields": [{"name": "query_id", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}, {"name": "NftCollection$Data", "header": null, "fields": [{"name": "next_item_index", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 32}}, {"name": "owner_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "collection_content", "type": {"kind": "simple", "type": "cell", "optional": false}}, {"name": "nft_price", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "authority_address", "type": {"kind": "simple", "type": "address", "optional": false}}]}, {"name": "NftItem$Data", "header": null, "fields": [{"name": "collection_address", "type": {"kind": "simple", "type": "address", "optional": false}}, {"name": "item_index", "type": {"kind": "simple", "type": "int", "optional": false, "format": 257}}, {"name": "is_initialized", "type": {"kind": "simple", "type": "bool", "optional": false}}, {"name": "owner", "type": {"kind": "simple", "type": "address", "optional": true}}, {"name": "individual_content", "type": {"kind": "simple", "type": "cell", "optional": true}}, {"name": "authority_address", "type": {"kind": "simple", "type": "address", "optional": true}}, {"name": "revoked_at", "type": {"kind": "simple", "type": "uint", "optional": false, "format": 64}}]}], "receivers": [{"receiver": "internal", "message": {"kind": "typed", "type": "Transfer"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "GetStaticData"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "ProveOwnership"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "RequestOwner"}}, {"receiver": "internal", "message": {"kind": "text", "text": "Destroy"}}, {"receiver": "internal", "message": {"kind": "typed", "type": "Revoke"}}], "getters": [{"name": "get_nft_data", "arguments": [], "returnType": {"kind": "simple", "type": "GetNftData", "optional": false}}, {"name": "get_authority_address", "arguments": [], "returnType": {"kind": "simple", "type": "address", "optional": true}}, {"name": "get_revoked_time", "arguments": [], "returnType": {"kind": "simple", "type": "int", "optional": false, "format": 257}}], "errors": {"2": {"message": "Stack underflow"}, "3": {"message": "Stack overflow"}, "4": {"message": "Integer overflow"}, "5": {"message": "Integer out of expected range"}, "6": {"message": "Invalid opcode"}, "7": {"message": "Type check error"}, "8": {"message": "Cell overflow"}, "9": {"message": "Cell underflow"}, "10": {"message": "Dictionary error"}, "11": {"message": "'Unknown' error"}, "12": {"message": "Fatal error"}, "13": {"message": "Out of gas error"}, "14": {"message": "Virtualization error"}, "32": {"message": "Action list is invalid"}, "33": {"message": "Action list is too long"}, "34": {"message": "Action is invalid or not supported"}, "35": {"message": "Invalid source address in outbound message"}, "36": {"message": "Invalid destination address in outbound message"}, "37": {"message": "Not enough TON"}, "38": {"message": "Not enough extra-currencies"}, "39": {"message": "Outbound message does not fit into a cell after rewriting"}, "40": {"message": "Cannot process a message"}, "41": {"message": "Library reference is null"}, "42": {"message": "Library change action error"}, "43": {"message": "Exceeded maximum number of cells in the library or the maximum depth of the Merkle tree"}, "50": {"message": "Account state size exceeded limits"}, "128": {"message": "Null reference exception"}, "129": {"message": "Invalid serialization prefix"}, "130": {"message": "Invalid incoming message"}, "131": {"message": "Constraints error"}, "132": {"message": "Access denied"}, "133": {"message": "Contract stopped"}, "134": {"message": "Invalid argument"}, "135": {"message": "Code of a contract was not found"}, "136": {"message": "Invalid address"}, "137": {"message": "Masterchain support is not enabled for this contract"}, "9397": {"message": "SBT cannot be transferred"}, "10990": {"message": "Already revoked"}, "14534": {"message": "Not owner"}, "17481": {"message": "Initialized tx need from collection"}, "23386": {"message": "Not from collection"}, "24690": {"message": "Non-sequential NFTs"}, "26825": {"message": "Only owner can withdraw"}, "31066": {"message": "No TON to withdraw"}, "41925": {"message": "Insufficient funds for minting"}, "42435": {"message": "Not authorized"}}, "interfaces": ["org.ton.introspection.v0", "org.ton.abi.ipfs.v0", "org.ton.deploy.lazy.v0", "org.ton.debug.v0", "org.ton.chain.workchain.v0"]}